@echo off
:: <PERSON><PERSON><PERSON> quyền Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo <PERSON><PERSON> chạy vớ<PERSON> quyền Administrator...
    cd /d "%~dp0"
    echo.
    echo === ĐĂNG KÝ TEST CONTEXT MENU ===
    python register_test_context_menu.py
    echo.
    echo Hoàn tất! H<PERSON><PERSON> thử nhấn chuột phải vào file PDF và chọn "Test Đổi tên PDF"
    echo.
    echo Để hủy đăng ký test, chạy: python register_test_context_menu.py unregister
    pause
) else (
    echo Cần quyền Administrator để đăng ký context menu.
    echo Đang khởi động lại với quyền Administrator...
    powershell -WindowStyle Hidden -Command "Start-Process cmd -ArgumentList '/c \"%~f0\"' -Verb RunAs"
)
