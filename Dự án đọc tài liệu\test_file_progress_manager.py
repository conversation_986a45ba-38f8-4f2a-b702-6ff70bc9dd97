#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra FileProgressManager compatibility với rename_pdf.py
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_file_progress_manager():
    """Test FileProgressManager có tương thích với rename_pdf.py không"""
    print("🧪 Testing FileProgressManager compatibility...")
    
    try:
        from batch_pdf_processor import FileProgressManager
        print("✅ FileProgressManager imported successfully")
        
        # Test callback function
        messages = []
        def test_callback(message):
            messages.append(message)
            print(f"   📝 {message}")
        
        # Create progress manager
        progress_manager = FileProgressManager(test_callback)
        print("✅ FileProgressManager created")
        
        # Test required attributes
        required_attrs = ['steps', 'current_step', 'total_steps', 'step_weights', 'total_weight']
        for attr in required_attrs:
            if hasattr(progress_manager, attr):
                print(f"   ✅ Has attribute: {attr}")
            else:
                print(f"   ❌ Missing attribute: {attr}")
                return False
        
        # Test set_steps method
        steps = [
            ("Kiểm tra file và cấu hình", 10),
            ("Chuyển đổi PDF thành ảnh", 20),
            ("OCR văn bản", 30),
            ("Trích xuất thông tin với AI", 25),
            ("Lưu kết quả", 15)
        ]
        
        progress_manager.set_steps(steps)
        print("✅ set_steps() called successfully")
        print(f"   📊 Total steps: {progress_manager.total_steps}")
        print(f"   📊 Total weight: {progress_manager.total_weight}")
        
        # Test step methods
        print("\n🔄 Testing step methods...")
        
        # Step 1
        progress_manager.start_step(0, "Testing step 1")
        progress_manager.update_step_progress(50, "Half way through step 1")
        progress_manager.complete_step("Step 1 done")
        
        # Step 2
        progress_manager.start_step(1, "Testing step 2")
        progress_manager.update_step_progress(100, "Step 2 complete")
        progress_manager.complete_step("Step 2 done")
        
        # Test completion
        progress_manager.set_completed(True, "All tests completed successfully")
        
        print(f"\n📋 Total messages captured: {len(messages)}")
        print("✅ All FileProgressManager methods work correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compatibility_with_rename_pdf():
    """Test compatibility với process_pdf_with_progress"""
    print("\n🧪 Testing compatibility with rename_pdf...")
    
    try:
        # Test import
        from batch_pdf_processor import FileProgressManager
        from rename_pdf import process_pdf_with_progress
        print("✅ Both modules imported successfully")
        
        # Create a fake PDF path for testing
        fake_pdf_path = "nonexistent_test.pdf"
        
        # Create progress manager
        messages = []
        def test_callback(message):
            messages.append(message)
            print(f"   📝 {message}")
        
        progress_manager = FileProgressManager(test_callback)
        
        # Set steps (this is what was missing before)
        steps = [
            ("Kiểm tra file và cấu hình", 10),
            ("Chuyển đổi PDF thành ảnh", 20),
            ("OCR văn bản", 30),
            ("Trích xuất thông tin với AI", 25),
            ("Lưu kết quả", 15)
        ]
        progress_manager.set_steps(steps)
        print("✅ Steps set successfully")
        
        # Test calling process_pdf_with_progress (should fail at file check, but not with attribute error)
        print("\n🔄 Testing process_pdf_with_progress call...")
        try:
            process_pdf_with_progress(fake_pdf_path, progress_manager)
            print("❌ Unexpected success - should have failed at file check")
        except Exception as e:
            error_msg = str(e)
            if "'FileProgressManager' object has no attribute 'steps'" in error_msg:
                print("❌ Still has attribute error - fix not working")
                return False
            else:
                print(f"✅ Expected error (not attribute error): {error_msg}")
                print("✅ FileProgressManager compatibility confirmed")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_processor_integration():
    """Test integration trong batch processor"""
    print("\n🧪 Testing batch processor integration...")
    
    try:
        from batch_pdf_processor import PDFProcessorThread, FileProgressManager
        from config_manager import ConfigManager
        print("✅ All components imported")
        
        # Test creating processor thread
        fake_files = ["test1.pdf", "test2.pdf"]
        config = ConfigManager()
        
        processor = PDFProcessorThread(fake_files, config)
        print("✅ PDFProcessorThread created")
        
        # Test FileProgressManager creation in thread context
        messages = []
        def test_callback(message):
            messages.append(message)
        
        file_progress = FileProgressManager(test_callback)
        
        # Test steps setup (like in the thread)
        steps = [
            ("Kiểm tra file và cấu hình", 10),
            ("Chuyển đổi PDF thành ảnh", 20),
            ("OCR văn bản", 30),
            ("Trích xuất thông tin với AI", 25),
            ("Lưu kết quả", 15)
        ]
        file_progress.set_steps(steps)
        print("✅ Steps setup in thread context works")
        
        # Test all required methods exist
        methods = ['start_step', 'update_step_progress', 'complete_step', 'set_completed', 'is_cancelled']
        for method in methods:
            if hasattr(file_progress, method):
                print(f"   ✅ Has method: {method}")
            else:
                print(f"   ❌ Missing method: {method}")
                return False
        
        print("✅ Batch processor integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 Testing FileProgressManager Fix\n")
    
    # Test 1: Basic functionality
    test1_result = test_file_progress_manager()
    
    # Test 2: Compatibility with rename_pdf
    test2_result = test_compatibility_with_rename_pdf()
    
    # Test 3: Batch processor integration
    test3_result = test_batch_processor_integration()
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"   Basic functionality: {'✅' if test1_result else '❌'}")
    print(f"   rename_pdf compatibility: {'✅' if test2_result else '❌'}")
    print(f"   Batch processor integration: {'✅' if test3_result else '❌'}")
    
    if all([test1_result, test2_result, test3_result]):
        print(f"\n🎉 All tests passed! FileProgressManager fix is working correctly.")
        print(f"\n💡 The error 'FileProgressManager' object has no attribute 'steps' should be resolved.")
        print(f"\n🚀 You can now run batch_pdf_processor.py and process multiple PDFs successfully!")
    else:
        print(f"\n❌ Some tests failed. Please check the implementation.")
        
    print(f"\n📋 What was fixed:")
    print(f"   ✅ Added missing 'steps' attribute to FileProgressManager")
    print(f"   ✅ Added set_steps() method to initialize steps")
    print(f"   ✅ Added step tracking attributes (current_step, total_steps, etc.)")
    print(f"   ✅ Made FileProgressManager compatible with rename_pdf.py expectations")
    print(f"   ✅ Added steps setup in PDFProcessorThread before calling process_pdf_with_progress")

if __name__ == "__main__":
    main()
