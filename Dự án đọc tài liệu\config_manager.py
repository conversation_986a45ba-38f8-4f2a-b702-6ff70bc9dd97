import json
import os
import gspread
from google.cloud import vision
import google.generativeai as genai
import openai
from groq import Groq
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QLineEdit, QPushButton, QComboBox,
                            QFileDialog, QMessageBox, QGroupBox, QFormLayout,
                            QTextEdit, QTabWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from google.oauth2 import service_account
import datetime
import copy
import threading

# Import progress dialog
try:
    from simple_progress import SimpleProgressDialog, SimpleProgressManager
    PROGRESS_AVAILABLE = True
except ImportError:
    PROGRESS_AVAILABLE = False
    print("Warning: simple_progress not available - progress dialogs disabled")

class ConfigManager:
    def __init__(self, base_dir=None):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.base_dir = base_dir if base_dir else self.script_dir
        self.config_file = os.path.join(self.base_dir, 'config.json')
        self.context_menu_config_file = os.path.join(self.base_dir, 'context_menu_config.json')
        self.changes_log_file = os.path.join(self.base_dir, 'config_changes.log')

        # Lưu trữ config ban đầu để so sánh thay đổi
        self.original_config = None
        self.original_context_menu_config = None

        self.default_config = {
            "ai_provider": "gemini",
            "models": {
                "gemini": {
                    "selected": "gemini-2.5-pro-preview-06-05",
                    "available": [
                        "gemini-2.5-pro-preview-06-05",
                        "gemini-2.5-flash-preview-05-20",
                        "gemini-2.0-flash",
                        "gemini-2.0-flash-lite"
                    ]
                },
                "openai": {
                    "selected": "gpt-4.1",
                    "available": [
                        "gpt-4.1",
                        "gpt-4.1-mini",
                        "gpt-4.1-nano",
                        "gpt-4o",
                        "gpt-4o-mini",
                        "gpt-4o-mini-tts",
                        "gpt-4o-transcribe",
                        "gpt-4o-mini-transcribe",
                        "gpt-4.5",
                        "gpt-4.5-preview",
                        "gpt-4-turbo",
                        "gpt-4-turbo-2024-04-09",
                        "gpt-4",
                        "gpt-4-0613",
                        "gpt-3.5-turbo",
                        "gpt-3.5-turbo-0125",
                        "o4-mini",
                        "o3",
                        "o3-mini",
                        "o1-preview",
                        "o1-mini"
                    ]
                },
                "grok": {
                    "selected": "grok-3",
                    "available": [
                        "grok-3",
                        "grok-3-vision",
                        "grok-beta",
                        "grok-vision-beta",
                        "grok-2-1212",
                        "grok-2-vision-1212",
                        "grok-2",
                        "grok-1.5"
                    ]
                }
            },
            "api_keys": {
                "google": "",
                "openai": "",
                "grok": ""
            },
            "google_sheet": {
                "url": "",
                "name": "",
                "credentials_file": "soy-tube-461903-r0-be576ddac8e6.json"
            }
        }
        self.config = self.default_config.copy()
        self.context_menu_config = {}
        self.load_config()
        self.load_context_menu_config()

        # Lưu config ban đầu để theo dõi thay đổi
        self.original_config = copy.deepcopy(self.config)
        self.original_context_menu_config = copy.deepcopy(self.context_menu_config)
        
    def load_config(self):
        """Load cấu hình từ file config.json"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # Cập nhật các giá trị từ file config vào default_config
                    for key, value in loaded_config.items():
                        if key in self.config:
                            if isinstance(value, dict):
                                self.config[key].update(value)
                            else:
                                self.config[key] = value
            except Exception as e:
                print(f"Lỗi khi đọc file config: {str(e)}")
                self.save_config()
        else:
            self.save_config()
            
    def load_context_menu_config(self):
        """Load cấu hình context menu từ file"""
        if os.path.exists(self.context_menu_config_file):
            try:
                with open(self.context_menu_config_file, 'r', encoding='utf-8') as f:
                    self.context_menu_config = json.load(f)
            except Exception as e:
                print(f"Lỗi khi đọc file context menu config: {str(e)}")
                self.context_menu_config = {}
        else:
            self.context_menu_config = {}

    def save_config(self):
        """Lưu cấu hình vào file config.json và ghi log thay đổi"""
        # Kiểm tra thay đổi trước khi lưu
        changes = self.detect_changes()

        # Lưu file config
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=4, ensure_ascii=False)

        # Ghi log thay đổi nếu có
        if changes:
            self.log_changes(changes)

        # Cập nhật config gốc
        self.original_config = copy.deepcopy(self.config)

        return changes

    def save_context_menu_config(self):
        """Lưu cấu hình context menu"""
        changes = self.detect_context_menu_changes()

        with open(self.context_menu_config_file, 'w', encoding='utf-8') as f:
            json.dump(self.context_menu_config, f, indent=4, ensure_ascii=False)

        if changes:
            self.log_context_menu_changes(changes)

        self.original_context_menu_config = copy.deepcopy(self.context_menu_config)

        return changes

    def detect_changes(self):
        """Phát hiện thay đổi trong config chính"""
        changes = []

        def compare_dict(original, current, path=""):
            for key in set(list(original.keys()) + list(current.keys())):
                current_path = f"{path}.{key}" if path else key

                if key not in original:
                    changes.append(f"ADDED: {current_path} = {current.get(key)}")
                elif key not in current:
                    changes.append(f"REMOVED: {current_path} = {original.get(key)}")
                elif isinstance(original[key], dict) and isinstance(current[key], dict):
                    compare_dict(original[key], current[key], current_path)
                elif original[key] != current[key]:
                    changes.append(f"CHANGED: {current_path} = {original[key]} -> {current[key]}")

        compare_dict(self.original_config, self.config)
        return changes

    def detect_context_menu_changes(self):
        """Phát hiện thay đổi trong context menu config"""
        changes = []

        def compare_dict(original, current, path=""):
            for key in set(list(original.keys()) + list(current.keys())):
                current_path = f"{path}.{key}" if path else key

                if key not in original:
                    changes.append(f"ADDED: {current_path} = {current.get(key)}")
                elif key not in current:
                    changes.append(f"REMOVED: {current_path} = {original.get(key)}")
                elif original[key] != current[key]:
                    changes.append(f"CHANGED: {current_path} = {original[key]} -> {current[key]}")

        compare_dict(self.original_context_menu_config, self.context_menu_config)
        return changes

    def log_changes(self, changes):
        """Ghi log thay đổi config chính"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        with open(self.changes_log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n=== CONFIG CHANGES - {timestamp} ===\n")
            for change in changes:
                f.write(f"{change}\n")
            f.write("=" * 50 + "\n")

    def log_context_menu_changes(self, changes):
        """Ghi log thay đổi context menu config"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        with open(self.changes_log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n=== CONTEXT MENU CONFIG CHANGES - {timestamp} ===\n")
            f.write("⚠️  QUAN TRỌNG: Thay đổi context menu có thể ảnh hưởng đến menu chuột phải PDF\n")
            for change in changes:
                f.write(f"{change}\n")
            f.write("💡 Lưu ý: Có thể cần đăng ký lại context menu sau khi thay đổi\n")
            f.write("=" * 50 + "\n")

    def get_changes_log(self):
        """Đọc log thay đổi"""
        if os.path.exists(self.changes_log_file):
            try:
                with open(self.changes_log_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                return f"Lỗi đọc log: {str(e)}"
        return "Chưa có thay đổi nào được ghi lại."

    def get_ai_provider(self):
        """Lấy AI provider đang được chọn"""
        return self.config.get('ai_provider', 'gemini')
        
    def get_selected_model(self):
        """Lấy model đang được chọn cho AI provider hiện tại"""
        provider = self.get_ai_provider()
        return self.config['models'][provider]['selected']
        
    def get_api_key(self, provider):
        """Lấy API key cho provider"""
        return self.config['api_keys'].get(provider, '')
        
    def get_google_sheet_url(self):
        """Lấy URL Google Sheet"""
        return self.config['google_sheet'].get('url', '')
        
    def get_google_sheet_name(self):
        """Lấy tên Google Sheet"""
        return self.config['google_sheet'].get('name', '')
        
    def get_google_credentials_file(self):
        """Lấy đường dẫn file credentials"""
        creds_file = self.config['google_sheet'].get('credentials_file', '')
        if not os.path.isabs(creds_file):
            # Nếu là đường dẫn tương đối, chuyển đổi thành tuyệt đối dựa trên base_dir
            return os.path.join(self.base_dir, creds_file)
        return creds_file
        
    def check_google_api_connection(self):
        """Kiểm tra kết nối Google API (Gemini)"""
        try:
            api_key = self.get_api_key('google')
            if not api_key:
                return False, "Chưa cấu hình Google API Key"
                
            # Kiểm tra Gemini API
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel("gemini-2.5-pro-preview-06-05")
            
            return True, "Kết nối Google API thành công"
        except Exception as e:
            return False, f"Lỗi kết nối Google API: {str(e)}"
            
    def check_google_vision_connection(self):
        """Kiểm tra kết nối Google Vision API"""
        try:
            credentials_file = self.get_google_credentials_file()
            if not os.path.exists(credentials_file):
                return False, f"Không tìm thấy file credentials: {credentials_file}"
            
            # Tạo credentials từ file JSON
            creds = service_account.Credentials.from_service_account_file(credentials_file)
                
            # Kiểm tra Vision API với credentials
            client = vision.ImageAnnotatorClient(credentials=creds)
            
            return True, "Kết nối Google Vision API thành công"
        except Exception as e:
            return False, f"Lỗi kết nối Google Vision API: {str(e)}"
            
    def check_openai_connection(self):
        """Kiểm tra kết nối OpenAI API - Latest version compatible"""
        try:
            api_key = self.get_api_key('openai')
            if not api_key:
                return False, "Chưa cấu hình OpenAI API Key"

            # Import OpenAI và kiểm tra version
            try:
                import openai
                version = getattr(openai, '__version__', '0.0.0')
                major_version = int(version.split('.')[0])
            except ImportError:
                return False, "Chưa cài đặt thư viện openai. Chạy: pip install openai"
            except (ValueError, AttributeError):
                major_version = 1  # Assume modern version if can't parse

            # Khởi tạo client dựa trên version
            client = None
            client_type = None

            try:
                if major_version >= 1:
                    # OpenAI v1.0+ - Modern client
                    client = openai.OpenAI(api_key=api_key)
                    client_type = "modern"
                else:
                    # OpenAI v0.x - Legacy client (deprecated)
                    openai.api_key = api_key
                    client = openai
                    client_type = "legacy"
            except Exception as client_error:
                return False, f"Lỗi khởi tạo OpenAI client: {str(client_error)}"

            # Test connection với multiple models (latest first)
            test_models = [
                "gpt-4o-mini",      # Latest cost-effective model
                "gpt-4o",           # Latest flagship model
                "gpt-3.5-turbo",    # Reliable fallback
                "gpt-4-turbo",      # High performance alternative
                "gpt-4"             # Standard GPT-4
            ]

            last_error = None

            for model in test_models:
                try:
                    if client_type == "modern":
                        # Modern API call with timeout and error handling
                        response = client.chat.completions.create(
                            model=model,
                            messages=[{"role": "user", "content": "Hi"}],
                            max_tokens=5,
                            timeout=15  # Add timeout for reliability
                        )
                        if response and response.choices and len(response.choices) > 0:
                            return True, f"Kết nối OpenAI API thành công (v{version}, model: {model})"
                    else:
                        # Legacy API call (for very old versions)
                        response = client.ChatCompletion.create(
                            model=model,
                            messages=[{"role": "user", "content": "Hi"}],
                            max_tokens=5,
                            timeout=15
                        )
                        if response and response.choices and len(response.choices) > 0:
                            return True, f"Kết nối OpenAI API thành công (legacy v{version}, model: {model})"

                except Exception as model_error:
                    last_error = str(model_error)
                    continue  # Try next model

            # Nếu tất cả models đều fail
            return False, f"Không thể kết nối với bất kỳ model nào. Lỗi cuối: {last_error}"

        except Exception as e:
            error_msg = str(e)

            # Provide specific error messages for common issues
            if "unexpected keyword argument" in error_msg:
                return False, "Lỗi version OpenAI. Cập nhật: pip install --upgrade openai"
            elif "authentication" in error_msg.lower() or "invalid api key" in error_msg.lower():
                return False, "API Key không hợp lệ. Kiểm tra lại OpenAI API Key"
            elif "rate limit" in error_msg.lower():
                return False, "Đã vượt quá rate limit. Thử lại sau vài phút"
            elif "timeout" in error_msg.lower():
                return False, "Timeout kết nối. Kiểm tra kết nối internet"
            elif "connection" in error_msg.lower():
                return False, "Lỗi kết nối mạng. Kiểm tra internet và firewall"
            else:
                return False, f"Lỗi kết nối OpenAI API: {error_msg}"

    def check_grok_connection(self):
        """Kiểm tra kết nối Grok API - Latest version compatible"""
        try:
            api_key = self.get_api_key('grok')
            if not api_key:
                return False, "Chưa cấu hình Grok API Key"

            # Import và khởi tạo Grok client với version compatibility
            try:
                from groq import Groq

                # Try different initialization methods for compatibility
                try:
                    # Modern Groq client (latest)
                    client = Groq(api_key=api_key)
                except TypeError as type_error:
                    if "proxies" in str(type_error):
                        # Handle proxies parameter issue
                        try:
                            # Try without any extra parameters
                            import groq
                            client = groq.Groq(api_key=api_key)
                        except Exception:
                            # Last resort - basic initialization
                            client = Groq()
                            client.api_key = api_key
                    else:
                        raise type_error

            except ImportError:
                return False, "Chưa cài đặt thư viện groq. Chạy: pip install groq"
            except Exception as client_error:
                error_msg = str(client_error)
                if "proxies" in error_msg:
                    return False, f"Lỗi Groq client version. Cập nhật: pip install --upgrade groq"
                else:
                    return False, f"Lỗi khởi tạo Grok client: {error_msg}"

            # Test với multiple models (latest first)
            test_models = [
                "grok-3",           # Latest model
                "grok-beta",        # Beta version
                "grok-2-1212",      # Stable version
                "grok-2"            # Fallback
            ]

            last_error = None

            for model in test_models:
                try:
                    response = client.chat.completions.create(
                        model=model,
                        messages=[{"role": "user", "content": "Hi"}],
                        max_tokens=5,
                        timeout=15
                    )
                    if response and response.choices and len(response.choices) > 0:
                        return True, f"Kết nối Grok API thành công (model: {model})"
                except Exception as model_error:
                    last_error = str(model_error)
                    continue  # Try next model

            # Nếu tất cả models đều fail
            return False, f"Không thể kết nối với bất kỳ Grok model nào. Lỗi cuối: {last_error}"

        except Exception as e:
            error_msg = str(e)

            # Provide specific error messages
            if "authentication" in error_msg.lower() or "invalid api key" in error_msg.lower():
                return False, "Grok API Key không hợp lệ. Kiểm tra lại API Key"
            elif "rate limit" in error_msg.lower():
                return False, "Đã vượt quá Grok rate limit. Thử lại sau"
            elif "timeout" in error_msg.lower():
                return False, "Timeout kết nối Grok. Kiểm tra internet"
            else:
                return False, f"Lỗi kết nối Grok API: {error_msg}"
            
    def check_google_sheet_connection(self):
        """Kiểm tra kết nối Google Sheet"""
        try:
            credentials_file = self.get_google_credentials_file()
            sheet_url = self.get_google_sheet_url()
            
            if not os.path.exists(credentials_file):
                return False, f"Không tìm thấy file credentials: {credentials_file}"
                
            if not sheet_url:
                return False, "Chưa cấu hình URL Google Sheet"
                
            gc = gspread.service_account(filename=credentials_file)
            if sheet_url.startswith('http'):
                sh = gc.open_by_url(sheet_url)
            else:
                sh = gc.open_by_key(sheet_url)
                
            # Cập nhật tên sheet vào config
            self.config['google_sheet']['name'] = sh.title
            self.save_config()
                
            return True, f"Kết nối Google Sheet thành công: {sh.title}"
        except Exception as e:
            return False, f"Lỗi kết nối Google Sheet: {str(e)}"
            
    def check_all_connections(self):
        """Kiểm tra tất cả các kết nối"""
        google_api_status, google_api_message = self.check_google_api_connection()
        google_vision_status, google_vision_message = self.check_google_vision_connection()
        openai_status, openai_message = self.check_openai_connection()
        grok_status, grok_message = self.check_grok_connection()
        sheet_status, sheet_message = self.check_google_sheet_connection()

        return {
            "google_api": {"status": google_api_status, "message": google_api_message},
            "google_vision": {"status": google_vision_status, "message": google_vision_message},
            "openai": {"status": openai_status, "message": openai_message},
            "grok": {"status": grok_status, "message": grok_message},
            "sheet": {"status": sheet_status, "message": sheet_message}
        }

    def check_all_connections_with_progress(self, progress_manager=None):
        """Kiểm tra tất cả các kết nối với progress dialog"""
        if not progress_manager:
            return self.check_all_connections()

        # Define steps
        steps = [
            ("Google API (Gemini)", 20),
            ("Google Vision API", 20),
            ("OpenAI API", 20),
            ("Grok API", 20),
            ("Google Sheet", 20)
        ]

        progress_manager.set_steps(steps)
        results = {}

        # Step 1: Google API
        progress_manager.start_step(0, "Kiểm tra Google API...")
        google_api_status, google_api_message = self.check_google_api_connection()
        results["google_api"] = {"status": google_api_status, "message": google_api_message}
        progress_manager.complete_step("Google API: " + ("✅" if google_api_status else "❌"))

        if progress_manager.is_cancelled():
            return results

        # Step 2: Google Vision
        progress_manager.start_step(1, "Kiểm tra Google Vision API...")
        google_vision_status, google_vision_message = self.check_google_vision_connection()
        results["google_vision"] = {"status": google_vision_status, "message": google_vision_message}
        progress_manager.complete_step("Google Vision: " + ("✅" if google_vision_status else "❌"))

        if progress_manager.is_cancelled():
            return results

        # Step 3: OpenAI
        progress_manager.start_step(2, "Kiểm tra OpenAI API...")
        openai_status, openai_message = self.check_openai_connection()
        results["openai"] = {"status": openai_status, "message": openai_message}
        progress_manager.complete_step("OpenAI: " + ("✅" if openai_status else "❌"))

        if progress_manager.is_cancelled():
            return results

        # Step 4: Grok
        progress_manager.start_step(3, "Kiểm tra Grok API...")
        grok_status, grok_message = self.check_grok_connection()
        results["grok"] = {"status": grok_status, "message": grok_message}
        progress_manager.complete_step("Grok: " + ("✅" if grok_status else "❌"))

        if progress_manager.is_cancelled():
            return results

        # Step 5: Google Sheet
        progress_manager.start_step(4, "Kiểm tra Google Sheet...")
        sheet_status, sheet_message = self.check_google_sheet_connection()
        results["sheet"] = {"status": sheet_status, "message": sheet_message}
        progress_manager.complete_step("Google Sheet: " + ("✅" if sheet_status else "❌"))

        return results

class ConfigWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.config = ConfigManager()
        self.init_ui()

    def show_changes_summary(self, changes, context_menu_changes=None):
        """Hiển thị tóm tắt thay đổi"""
        if not changes and not context_menu_changes:
            return

        message = "📝 Tóm tắt thay đổi đã lưu:\n\n"

        if changes:
            message += "🔧 Config chính:\n"
            for change in changes[:5]:  # Chỉ hiển thị 5 thay đổi đầu
                message += f"  • {change}\n"
            if len(changes) > 5:
                message += f"  ... và {len(changes) - 5} thay đổi khác\n"
            message += "\n"

        if context_menu_changes:
            message += "⚠️  Context Menu Config:\n"
            for change in context_menu_changes:
                message += f"  • {change}\n"
            message += "\n💡 Lưu ý: Có thể cần đăng ký lại context menu!\n"

        QMessageBox.information(self, "Thay đổi đã lưu", message)

    def init_ui(self):
        self.setWindowTitle('Cấu hình')
        self.setGeometry(100, 100, 900, 700)

        # Widget chính với tabs
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Tạo tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # Tab 1: Cấu hình chính
        self.create_main_config_tab()

        # Tab 2: Lịch sử thay đổi
        self.create_changes_log_tab()

        # Buttons chung
        button_layout = QHBoxLayout()

        save_button = QPushButton("💾 Lưu cấu hình")
        save_button.clicked.connect(self.save_config)
        button_layout.addWidget(save_button)

        refresh_log_button = QPushButton("🔄 Làm mới log")
        refresh_log_button.clicked.connect(self.refresh_changes_log)
        button_layout.addWidget(refresh_log_button)

        layout.addLayout(button_layout)

    def create_main_config_tab(self):
        """Tạo tab cấu hình chính"""
        main_tab = QWidget()
        self.tab_widget.addTab(main_tab, "⚙️ Cấu hình")
        layout = QVBoxLayout(main_tab)

        # AI Provider
        provider_group = QGroupBox("🤖 AI Provider")
        provider_layout = QFormLayout()

        self.provider_combo = QComboBox()
        self.provider_combo.addItems(["gemini", "openai", "grok"])
        self.provider_combo.setCurrentText(self.config.get_ai_provider())
        self.provider_combo.currentTextChanged.connect(self.update_model_combo)
        provider_layout.addRow("Provider:", self.provider_combo)

        self.model_combo = QComboBox()
        self.update_model_combo(self.config.get_ai_provider())
        provider_layout.addRow("Model:", self.model_combo)

        provider_group.setLayout(provider_layout)
        layout.addWidget(provider_group)

        # API Keys
        api_group = QGroupBox("🔑 API Keys")
        api_layout = QFormLayout()

        self.google_api_key = QLineEdit()
        self.google_api_key.setText(self.config.get_api_key('google'))
        self.google_api_key.setEchoMode(QLineEdit.Password)
        api_layout.addRow("Google API Key:", self.google_api_key)

        self.openai_api_key = QLineEdit()
        self.openai_api_key.setText(self.config.get_api_key('openai'))
        self.openai_api_key.setEchoMode(QLineEdit.Password)
        api_layout.addRow("OpenAI API Key:", self.openai_api_key)

        self.grok_api_key = QLineEdit()
        self.grok_api_key.setText(self.config.get_api_key('grok'))
        self.grok_api_key.setEchoMode(QLineEdit.Password)
        api_layout.addRow("Grok API Key:", self.grok_api_key)

        api_group.setLayout(api_layout)
        layout.addWidget(api_group)

        # Google Sheet
        sheet_group = QGroupBox("📊 Google Sheet")
        sheet_layout = QFormLayout()

        self.sheet_url = QLineEdit()
        self.sheet_url.setText(self.config.get_google_sheet_url())
        self.sheet_url.textChanged.connect(self.on_sheet_url_changed)
        sheet_layout.addRow("Sheet URL:", self.sheet_url)

        self.sheet_name = QLineEdit()
        self.sheet_name.setText(self.config.get_google_sheet_name())
        self.sheet_name.setReadOnly(True)  # Chỉ đọc, không cho phép chỉnh sửa
        sheet_layout.addRow("Sheet Name:", self.sheet_name)

        credentials_layout = QHBoxLayout()
        self.credentials_file = QLineEdit()
        self.credentials_file.setText(self.config.get_google_credentials_file())
        credentials_layout.addWidget(self.credentials_file)

        browse_button = QPushButton("📁 Browse")
        browse_button.clicked.connect(self.browse_credentials)
        credentials_layout.addWidget(browse_button)

        sheet_layout.addRow("Credentials File:", credentials_layout)

        sheet_group.setLayout(sheet_layout)
        layout.addWidget(sheet_group)

        # Test Buttons
        test_group = QGroupBox("🔍 Kiểm tra kết nối")
        test_layout = QHBoxLayout()

        test_google_api = QPushButton("🤖 Test Google API")
        test_google_api.clicked.connect(lambda: self.test_connection('google_api'))
        test_layout.addWidget(test_google_api)

        test_google_vision = QPushButton("👁️ Test Google Vision")
        test_google_vision.clicked.connect(lambda: self.test_connection('google_vision'))
        test_layout.addWidget(test_google_vision)

        test_openai = QPushButton("🧠 Test OpenAI")
        test_openai.clicked.connect(lambda: self.test_connection('openai'))
        test_layout.addWidget(test_openai)

        test_grok = QPushButton("⚡ Test Grok")
        test_grok.clicked.connect(lambda: self.test_connection('grok'))
        test_layout.addWidget(test_grok)

        test_sheet = QPushButton("📊 Test Google Sheet")
        test_sheet.clicked.connect(lambda: self.test_connection('sheet'))
        test_layout.addWidget(test_sheet)

        # Test all button
        test_all = QPushButton("🔍 Test All Connections")
        test_all.clicked.connect(self.test_all_connections)
        test_all.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        test_layout.addWidget(test_all)

        test_group.setLayout(test_layout)
        layout.addWidget(test_group)

    def create_changes_log_tab(self):
        """Tạo tab lịch sử thay đổi"""
        log_tab = QWidget()
        self.tab_widget.addTab(log_tab, "📝 Lịch sử thay đổi")
        layout = QVBoxLayout(log_tab)

        # Text area để hiển thị log
        self.changes_log_text = QTextEdit()
        self.changes_log_text.setReadOnly(True)
        self.changes_log_text.setFont(self.get_monospace_font())
        layout.addWidget(self.changes_log_text)

        # Load log ban đầu
        self.refresh_changes_log()

    def get_monospace_font(self):
        """Lấy font monospace cho hiển thị log"""
        from PyQt5.QtGui import QFont
        font = QFont("Consolas", 9)
        if not font.exactMatch():
            font = QFont("Courier New", 9)
        return font

    def refresh_changes_log(self):
        """Làm mới nội dung log"""
        log_content = self.config.get_changes_log()
        self.changes_log_text.setPlainText(log_content)

        # Scroll xuống cuối
        cursor = self.changes_log_text.textCursor()
        cursor.movePosition(cursor.End)
        self.changes_log_text.setTextCursor(cursor)
        
    def update_model_combo(self, provider):
        self.model_combo.clear()
        models = self.config.config['models'][provider]['available']
        self.model_combo.addItems(models)
        self.model_combo.setCurrentText(self.config.config['models'][provider]['selected'])
        
    def browse_credentials(self):
        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "Chọn file credentials",
            "",
            "JSON Files (*.json)"
        )
        if file_name:
            # Khi chọn file, lưu đường dẫn tuyệt đối
            self.credentials_file.setText(file_name)
            
    def on_sheet_url_changed(self):
        """Khi URL sheet thay đổi, tự động kiểm tra và lấy tên sheet"""
        if self.sheet_url.text().strip():
            self.test_connection('sheet')
            
    def test_connection(self, connection_type):
        if connection_type == 'google_api':
            status, message = self.config.check_google_api_connection()
        elif connection_type == 'google_vision':
            status, message = self.config.check_google_vision_connection()
        elif connection_type == 'openai':
            status, message = self.config.check_openai_connection()
        elif connection_type == 'grok':
            status, message = self.config.check_grok_connection()
        elif connection_type == 'sheet':
            status, message = self.config.check_google_sheet_connection()
            if status:
                # Cập nhật tên sheet vào giao diện
                self.sheet_name.setText(self.config.get_google_sheet_name())
            
        QMessageBox.information(
            self,
            "Kết quả kiểm tra",
            message
        )

    def test_all_connections(self):
        """Test tất cả connections với progress dialog"""
        if not PROGRESS_AVAILABLE:
            # Fallback: test without progress
            results = self.config.check_all_connections()
            self.show_all_test_results(results)
            return

        def test_work(dialog):
            try:
                progress_manager = SimpleProgressManager("Kiểm tra tất cả kết nối", dialog)
                results = self.config.check_all_connections_with_progress(progress_manager)

                if not progress_manager.is_cancelled():
                    # Count successful connections
                    success_count = sum(1 for r in results.values() if r["status"])
                    total_count = len(results)

                    if success_count == total_count:
                        progress_manager.set_completed(True, f"🎉 Tất cả {total_count} kết nối thành công!")
                    else:
                        progress_manager.set_completed(True, f"⚠️ {success_count}/{total_count} kết nối thành công")

                    # Show detailed results after dialog closes
                    import time
                    time.sleep(1)  # Wait a bit before showing results
                    self.show_all_test_results(results)
                else:
                    progress_manager.set_completed(False, "❌ Đã hủy kiểm tra")

            except Exception as e:
                progress_manager.set_completed(False, f"❌ Lỗi: {str(e)}")

        # Show progress dialog
        try:
            dialog = SimpleProgressDialog("Kiểm tra kết nối")
            dialog.show_and_run(test_work)
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể hiển thị progress dialog: {str(e)}")
            # Fallback
            results = self.config.check_all_connections()
            self.show_all_test_results(results)

    def show_all_test_results(self, results):
        """Hiển thị kết quả test tất cả connections"""
        message = "📋 Kết quả kiểm tra tất cả kết nối:\n\n"

        for service, result in results.items():
            status_icon = "✅" if result["status"] else "❌"
            service_name = {
                "google_api": "Google API (Gemini)",
                "google_vision": "Google Vision API",
                "openai": "OpenAI API",
                "grok": "Grok API",
                "sheet": "Google Sheet"
            }.get(service, service)

            message += f"{status_icon} {service_name}\n"
            message += f"   {result['message']}\n\n"

        # Count results
        success_count = sum(1 for r in results.values() if r["status"])
        total_count = len(results)

        if success_count == total_count:
            title = "✅ Tất cả kết nối thành công"
        elif success_count > 0:
            title = f"⚠️ {success_count}/{total_count} kết nối thành công"
        else:
            title = "❌ Tất cả kết nối thất bại"

        QMessageBox.information(self, title, message)

    def save_config(self):
        """Lưu cấu hình với theo dõi thay đổi"""
        # Cập nhật AI provider và model
        provider = self.provider_combo.currentText()
        self.config.config['ai_provider'] = provider
        self.config.config['models'][provider]['selected'] = self.model_combo.currentText()

        # Cập nhật API keys
        self.config.config['api_keys']['google'] = self.google_api_key.text()
        self.config.config['api_keys']['openai'] = self.openai_api_key.text()
        self.config.config['api_keys']['grok'] = self.grok_api_key.text()

        # Cập nhật Google Sheet
        self.config.config['google_sheet']['url'] = self.sheet_url.text()
        self.config.config['google_sheet']['credentials_file'] = self.credentials_file.text()

        # Lưu cấu hình và lấy thay đổi
        changes = self.config.save_config()

        # Kiểm tra thay đổi context menu nếu có
        context_menu_changes = None
        if os.path.exists(self.config.context_menu_config_file):
            # Reload context menu config để kiểm tra thay đổi
            self.config.load_context_menu_config()
            context_menu_changes = self.config.detect_context_menu_changes()
            if context_menu_changes:
                self.config.save_context_menu_config()

        # Hiển thị thông báo với tóm tắt thay đổi
        if changes or context_menu_changes:
            self.show_changes_summary(changes, context_menu_changes)
            # Làm mới log
            self.refresh_changes_log()
        else:
            QMessageBox.information(self, "✅ Thành công", "Đã lưu cấu hình (không có thay đổi)")

if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    window = ConfigWindow()
    window.show()
    sys.exit(app.exec_()) 