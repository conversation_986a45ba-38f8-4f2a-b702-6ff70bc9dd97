#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Đăng ký context menu cho test simple
"""

import os
import sys
import winreg

def register_test_simple():
    """Đăng ký context menu test simple"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Tìm Python
        python_paths = [
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
            r"C:\Python313\pythonw.exe",
            "pythonw.exe"
        ]
        
        python_path = "pythonw.exe"
        for path in python_paths:
            if os.path.exists(path):
                python_path = path
                break
        
        script_path = os.path.join(script_dir, "test_simple.py")
        
        print(f"Python: {python_path}")
        print(f"Script: {script_path}")
        
        # Xóa key cũ
        try:
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\TestSimple\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\TestSimple")
        except:
            pass
        
        # Tạo key mới
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\TestSimple") as key:
            winreg.SetValue(key, "", winreg.REG_SZ, "Test Simple")
        
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\TestSimple\command") as key:
            command = f'"{python_path}" "{script_path}" "%1"'
            winreg.SetValue(key, "", winreg.REG_SZ, command)
            print(f"Command: {command}")
        
        print("✓ Đăng ký Test Simple thành công!")
        print("Hãy thử nhấn chuột phải vào file PDF và chọn 'Test Simple'")
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def main():
    print("=== ĐĂNG KÝ TEST SIMPLE ===")
    register_test_simple()

if __name__ == "__main__":
    main()
