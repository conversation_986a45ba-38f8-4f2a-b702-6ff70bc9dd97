#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để so sánh performance giữa Sequential và Batch Parallel processing
"""

import sys
import os
import time
import tempfile
import shutil
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_batch_parallel_import():
    """Test import batch parallel processor"""
    print("🧪 Testing Batch Parallel Processor import...")
    
    try:
        from batch_parallel_processor import BatchParallelProcessor
        print("✅ BatchParallelProcessor imported successfully")
        
        # Test dependencies
        try:
            from config_manager import ConfigManager
            print("✅ ConfigManager available")
        except ImportError:
            print("❌ ConfigManager not available")
            return False
        
        try:
            import concurrent.futures
            print("✅ concurrent.futures available")
        except ImportError:
            print("❌ concurrent.futures not available")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_batch_parallel_logic():
    """Test batch parallel processing logic"""
    print("\n🧪 Testing Batch Parallel Processing Logic...")
    
    try:
        from batch_parallel_processor import BatchParallelProcessor
        from config_manager import ConfigManager
        
        # Create fake PDF files for testing
        fake_files = [
            "test1.pdf",
            "test2.pdf", 
            "test3.pdf"
        ]
        
        # Create config
        config = ConfigManager()
        
        # Test callback
        messages = []
        def test_callback(message):
            messages.append(message)
            print(f"   📝 {message}")
        
        # Create batch processor
        processor = BatchParallelProcessor(fake_files, config, test_callback)
        print("✅ BatchParallelProcessor created")
        
        # Test data structure
        print(f"   📊 File data initialized for {len(processor.file_data)} files")
        print(f"   📊 Results initialized for {len(processor.results)} files")
        
        # Test step 1 (will fail because files don't exist, but that's expected)
        print("\n🔄 Testing Step 1: Validate files...")
        result = processor.step1_validate_all_files()
        print(f"   📊 Step 1 result: {result} (expected False for fake files)")
        
        print("✅ Batch parallel logic test completed")
        return True
        
    except Exception as e:
        print(f"❌ Logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_processing_modes():
    """So sánh performance giữa Sequential và Batch Parallel"""
    print("\n📊 Comparing Processing Modes...")
    
    print("🔄 Sequential Processing:")
    print("   📋 Logic: File 1 → File 2 → File 3")
    print("   ⏱️  Time: (Convert1 + OCR1 + AI1 + Save1) + (Convert2 + OCR2 + AI2 + Save2) + ...")
    print("   🔧 Pros: Simple, stable, less memory")
    print("   ❌ Cons: Slow, no parallelization")
    
    print("\n🚀 Batch Parallel Processing:")
    print("   📋 Logic: All Convert → All OCR → All AI → All Save")
    print("   ⏱️  Time: Max(Convert1,2,3) + Max(OCR1,2,3) + Max(AI1,2,3) + Sum(Save1,2,3)")
    print("   🔧 Pros: Fast, efficient, parallel")
    print("   ❌ Cons: More memory, complex")
    
    print("\n📈 Expected Performance Improvement:")
    
    # Simulate timing
    files = 5
    convert_time = 3  # seconds per file
    ocr_time = 10     # seconds per file  
    ai_time = 5       # seconds per file
    save_time = 2     # seconds per file
    
    # Sequential
    sequential_time = files * (convert_time + ocr_time + ai_time + save_time)
    
    # Batch Parallel (assuming 3 workers for parallel steps)
    parallel_convert = convert_time  # All files converted in parallel
    parallel_ocr = ocr_time         # All files OCR'd in parallel
    parallel_ai = ai_time           # All files AI processed in parallel
    sequential_save = files * save_time  # Save must be sequential
    
    batch_parallel_time = parallel_convert + parallel_ocr + parallel_ai + sequential_save
    
    improvement = ((sequential_time - batch_parallel_time) / sequential_time) * 100
    
    print(f"   📊 {files} files example:")
    print(f"   ⏱️  Sequential: {sequential_time}s")
    print(f"   ⚡ Batch Parallel: {batch_parallel_time}s")
    print(f"   🚀 Improvement: {improvement:.1f}% faster")
    print(f"   💾 Time saved: {sequential_time - batch_parallel_time}s")

def show_batch_parallel_steps():
    """Hiển thị chi tiết các bước trong Batch Parallel"""
    print("\n📋 Batch Parallel Processing Steps:")
    print("=" * 50)
    
    steps = [
        ("Bước 1: Validate All Files", "Kiểm tra tất cả file PDF hợp lệ", "Sequential"),
        ("Bước 2: Convert All PDFs", "Chuyển đổi TẤT CẢ PDF → ảnh", "Parallel (3 workers)"),
        ("Bước 3: OCR All Images", "OCR TẤT CẢ ảnh → text", "Parallel (2 workers)"),
        ("Bước 4: AI Extract All", "AI extract TẤT CẢ thông tin", "Parallel (2 workers)"),
        ("Bước 5: Rename & Save All", "Đổi tên và lưu TẤT CẢ file", "Sequential (avoid conflicts)")
    ]
    
    for i, (step, description, mode) in enumerate(steps, 1):
        print(f"\n{i}. {step}")
        print(f"   📝 {description}")
        print(f"   🔧 Mode: {mode}")
        
        if "Parallel" in mode:
            print(f"   ⚡ Benefit: Xử lý nhiều file cùng lúc")
        else:
            print(f"   🔒 Reason: Tránh conflict hoặc cần tuần tự")

def show_usage_guide():
    """Hiển thị hướng dẫn sử dụng"""
    print("\n📖 Usage Guide:")
    print("=" * 30)
    
    print("\n🚀 Cách sử dụng Batch Parallel:")
    print("1. Chạy: python batch_pdf_processor.py")
    print("2. Thêm nhiều file PDF")
    print("3. Chọn 'Chế độ xử lý': Batch Parallel (Đồng loạt theo bước)")
    print("4. Nhấn '🚀 Bắt đầu xử lý'")
    print("5. Xem progress theo từng bước cho tất cả file")
    
    print("\n⚙️ Settings:")
    print("   📄 Tiếp tục khi lỗi: Không dừng khi 1 file bị lỗi")
    print("   📜 Tự động cuộn: Auto scroll log")
    print("   🔍 Hiển thị chi tiết: Show detailed progress")
    print("   🚀 Chế độ xử lý: Sequential vs Batch Parallel")
    
    print("\n💡 Khi nào dùng Batch Parallel:")
    print("   ✅ Có nhiều file (5+ files)")
    print("   ✅ File không quá lớn (< 50MB each)")
    print("   ✅ Máy có RAM đủ (8GB+)")
    print("   ✅ Muốn xử lý nhanh")
    
    print("\n💡 Khi nào dùng Sequential:")
    print("   ✅ Ít file (1-3 files)")
    print("   ✅ File rất lớn (> 50MB each)")
    print("   ✅ RAM hạn chế (< 4GB)")
    print("   ✅ Muốn ổn định hơn")

def main():
    print("🚀 Testing Batch Parallel PDF Processor\n")
    
    # Test 1: Import
    import_success = test_batch_parallel_import()
    
    # Test 2: Logic
    if import_success:
        logic_success = test_batch_parallel_logic()
    else:
        logic_success = False
    
    # Show comparison
    compare_processing_modes()
    
    # Show detailed steps
    show_batch_parallel_steps()
    
    # Show usage guide
    show_usage_guide()
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"   Import test: {'✅' if import_success else '❌'}")
    print(f"   Logic test: {'✅' if logic_success else '❌'}")
    
    if import_success and logic_success:
        print(f"\n🎉 Batch Parallel Processor is ready!")
        print(f"\n💡 Expected benefits:")
        print(f"   ⚡ 60-80% faster processing")
        print(f"   🔄 Parallel PDF conversion")
        print(f"   🔄 Parallel OCR processing")
        print(f"   🔄 Parallel AI extraction")
        print(f"   📊 Better resource utilization")
        
        print(f"\n🚀 To test:")
        print(f"   python batch_pdf_processor.py")
        print(f"   → Add multiple PDFs")
        print(f"   → Select 'Batch Parallel' mode")
        print(f"   → Start processing")
        print(f"   → Watch the speed difference!")
    else:
        print(f"\n❌ Some tests failed. Check dependencies.")

if __name__ == "__main__":
    main()
