#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra context menu
"""

import sys
import os
import json

def main():
    print("=== TEST CONTEXT MENU ===")
    print(f"Python version: {sys.version}")
    print(f"Script path: {__file__}")
    print(f"Working directory: {os.getcwd()}")
    print(f"Arguments: {sys.argv}")
    
    # Kiểm tra file cấu hình
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_file = os.path.join(script_dir, 'context_menu_config.json')
    
    print(f"Config file: {config_file}")
    print(f"Config exists: {os.path.exists(config_file)}")
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("Config content:")
            for key, value in config.items():
                print(f"  {key}: {value}")
        except Exception as e:
            print(f"Error reading config: {e}")
    
    # Kiểm tra argument
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
        print(f"PDF path: {pdf_path}")
        print(f"PDF exists: {os.path.exists(pdf_path)}")
        print(f"PDF is file: {os.path.isfile(pdf_path) if os.path.exists(pdf_path) else False}")
    else:
        print("No PDF path provided")
    
    print("=== END TEST ===")

if __name__ == "__main__":
    main()
