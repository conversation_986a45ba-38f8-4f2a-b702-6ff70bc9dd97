#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch Parallel PDF Processor - Xử lý nhiều PDF theo batch steps
"""

import sys
import os
import time
import threading
import concurrent.futures
from datetime import datetime
from pathlib import Path
import traceback
import re
from collections import defaultdict

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our modules
try:
    from config_manager import ConfigManager
    from pdf2image import convert_from_path
    from google.cloud import vision
    from google.oauth2 import service_account
    import gspread
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Some modules not available: {e}")
    MODULES_AVAILABLE = False

class BatchParallelProcessor:
    """Xử lý batch PDF với parallel processing theo từng bước"""
    
    def __init__(self, pdf_files, config, progress_callback=None):
        self.pdf_files = pdf_files
        self.config = config
        self.progress_callback = progress_callback
        self.cancelled = False
        
        # Data storage cho từng bước
        self.file_data = {}  # {file_path: {images: [], text: "", info: {}, etc.}}
        self.results = {}    # {file_path: {success: bool, message: str}}
        
        # Initialize file data
        for pdf_path in pdf_files:
            self.file_data[pdf_path] = {
                'original_path': pdf_path,
                'filename': os.path.basename(pdf_path),
                'images': [],
                'text': '',
                'extracted_info': {},
                'new_filename': '',
                'new_path': '',
                'summary': '',
                'processing_time': 0,
                'step_times': {}
            }
            self.results[pdf_path] = {'success': False, 'message': ''}
    
    def log_progress(self, message):
        """Log progress message"""
        if self.progress_callback:
            self.progress_callback(message)
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def is_cancelled(self):
        """Check if processing is cancelled"""
        return self.cancelled
    
    def cancel(self):
        """Cancel processing"""
        self.cancelled = True
    
    def process_all_files(self):
        """Main method để xử lý tất cả file theo batch steps"""
        start_time = time.time()
        total_files = len(self.pdf_files)
        
        try:
            self.log_progress(f"🚀 Bắt đầu batch processing {total_files} file PDF...")
            
            # Bước 1: Kiểm tra tất cả file PDF
            if not self.step1_validate_all_files():
                return False
            
            if self.is_cancelled():
                return False
            
            # Bước 2: Chuyển đổi TẤT CẢ PDF thành ảnh (parallel)
            if not self.step2_convert_all_pdfs():
                return False
            
            if self.is_cancelled():
                return False
            
            # Bước 3: OCR TẤT CẢ ảnh (parallel)
            if not self.step3_ocr_all_images():
                return False
            
            if self.is_cancelled():
                return False
            
            # Bước 4: AI extract TẤT CẢ thông tin (parallel)
            if not self.step4_extract_all_info():
                return False
            
            if self.is_cancelled():
                return False
            
            # Bước 5: Đổi tên và lưu TẤT CẢ file (sequential để tránh conflict)
            if not self.step5_rename_and_save_all():
                return False
            
            # Tóm tắt kết quả
            total_time = time.time() - start_time
            success_count = sum(1 for r in self.results.values() if r['success'])
            
            self.log_progress(f"🎉 Hoàn thành batch processing!")
            self.log_progress(f"✅ Thành công: {success_count}/{total_files} file")
            self.log_progress(f"⏱️ Tổng thời gian: {total_time:.2f}s")
            self.log_progress(f"📊 Trung bình: {total_time/total_files:.2f}s/file")
            
            return success_count > 0
            
        except Exception as e:
            self.log_progress(f"❌ Lỗi nghiêm trọng trong batch processing: {e}")
            traceback.print_exc()
            return False
    
    def step1_validate_all_files(self):
        """Bước 1: Kiểm tra tất cả file PDF"""
        self.log_progress("📋 Bước 1: Kiểm tra tất cả file PDF...")
        
        valid_files = []
        invalid_files = []
        
        for pdf_path in self.pdf_files:
            if self.is_cancelled():
                return False
                
            filename = os.path.basename(pdf_path)
            
            # Kiểm tra file tồn tại
            if not os.path.exists(pdf_path):
                self.results[pdf_path] = {'success': False, 'message': f'File không tồn tại: {filename}'}
                invalid_files.append(filename)
                continue
            
            # Kiểm tra extension
            if not pdf_path.lower().endswith('.pdf'):
                self.results[pdf_path] = {'success': False, 'message': f'Không phải file PDF: {filename}'}
                invalid_files.append(filename)
                continue
            
            # Kiểm tra kích thước
            try:
                file_size = os.path.getsize(pdf_path)
                if file_size == 0:
                    self.results[pdf_path] = {'success': False, 'message': f'File rỗng: {filename}'}
                    invalid_files.append(filename)
                    continue
                
                self.file_data[pdf_path]['file_size'] = file_size
                valid_files.append(pdf_path)
                
            except Exception as e:
                self.results[pdf_path] = {'success': False, 'message': f'Lỗi đọc file {filename}: {e}'}
                invalid_files.append(filename)
        
        # Cập nhật danh sách file hợp lệ
        self.pdf_files = valid_files
        
        self.log_progress(f"✅ Bước 1 hoàn thành: {len(valid_files)} file hợp lệ, {len(invalid_files)} file lỗi")
        
        if invalid_files:
            self.log_progress(f"❌ File lỗi: {', '.join(invalid_files[:3])}")
            if len(invalid_files) > 3:
                self.log_progress(f"   ... và {len(invalid_files) - 3} file khác")
        
        return len(valid_files) > 0
    
    def step2_convert_all_pdfs(self):
        """Bước 2: Chuyển đổi TẤT CẢ PDF thành ảnh (parallel)"""
        self.log_progress("🖼️ Bước 2: Chuyển đổi tất cả PDF thành ảnh...")
        
        def convert_single_pdf(pdf_path):
            """Convert một PDF thành ảnh"""
            try:
                filename = os.path.basename(pdf_path)
                start_time = time.time()
                
                self.log_progress(f"   🔄 Đang convert: {filename}")
                
                images = convert_from_path(pdf_path)
                convert_time = time.time() - start_time
                
                self.file_data[pdf_path]['images'] = images
                self.file_data[pdf_path]['step_times']['convert'] = convert_time
                
                self.log_progress(f"   ✅ Convert xong: {filename} ({len(images)} trang, {convert_time:.2f}s)")
                return True
                
            except Exception as e:
                self.log_progress(f"   ❌ Lỗi convert {filename}: {e}")
                self.results[pdf_path] = {'success': False, 'message': f'Lỗi convert PDF: {e}'}
                return False
        
        # Parallel processing với ThreadPoolExecutor
        success_count = 0
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            # Submit tất cả tasks
            future_to_pdf = {executor.submit(convert_single_pdf, pdf_path): pdf_path 
                            for pdf_path in self.pdf_files}
            
            # Collect results
            for future in concurrent.futures.as_completed(future_to_pdf):
                if self.is_cancelled():
                    executor.shutdown(wait=False)
                    return False
                    
                pdf_path = future_to_pdf[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                except Exception as e:
                    filename = os.path.basename(pdf_path)
                    self.log_progress(f"   ❌ Exception convert {filename}: {e}")
                    self.results[pdf_path] = {'success': False, 'message': f'Exception convert: {e}'}
        
        # Cập nhật danh sách file thành công
        successful_files = [pdf_path for pdf_path in self.pdf_files 
                          if self.file_data[pdf_path]['images']]
        self.pdf_files = successful_files
        
        self.log_progress(f"✅ Bước 2 hoàn thành: {success_count} file convert thành công")
        return success_count > 0
    
    def step3_ocr_all_images(self):
        """Bước 3: OCR TẤT CẢ ảnh thành text (parallel)"""
        self.log_progress("📝 Bước 3: OCR tất cả ảnh thành text...")
        
        # Khởi tạo Vision client
        try:
            credentials_file = self.config.get_google_credentials_file()
            creds = service_account.Credentials.from_service_account_file(credentials_file)
            vision_client = vision.ImageAnnotatorClient(credentials=creds)
            self.log_progress("   ✅ Vision client khởi tạo thành công")
        except Exception as e:
            self.log_progress(f"   ❌ Lỗi khởi tạo Vision client: {e}")
            return False
        
        def ocr_single_file(pdf_path):
            """OCR tất cả ảnh của một file"""
            try:
                filename = os.path.basename(pdf_path)
                images = self.file_data[pdf_path]['images']
                start_time = time.time()
                
                self.log_progress(f"   🔄 Đang OCR: {filename} ({len(images)} trang)")
                
                full_text = []
                for i, image in enumerate(images, 1):
                    if self.is_cancelled():
                        return False
                    
                    # Convert image to bytes
                    from io import BytesIO
                    buf = BytesIO()
                    image.save(buf, format='PNG')
                    image_data = buf.getvalue()
                    
                    # OCR với Vision API (sử dụng method đúng)
                    vision_image = vision.Image(content=image_data)
                    response = vision_client.text_detection(image=vision_image)

                    if response.text_annotations:
                        page_text = response.text_annotations[0].description
                        full_text.append(page_text)
                    else:
                        full_text.append("")
                
                combined_text = "\n".join(full_text)
                ocr_time = time.time() - start_time
                
                self.file_data[pdf_path]['text'] = combined_text
                self.file_data[pdf_path]['step_times']['ocr'] = ocr_time
                
                char_count = len(combined_text)
                self.log_progress(f"   ✅ OCR xong: {filename} ({char_count:,} ký tự, {ocr_time:.2f}s)")
                return True
                
            except Exception as e:
                self.log_progress(f"   ❌ Lỗi OCR {filename}: {e}")
                self.results[pdf_path] = {'success': False, 'message': f'Lỗi OCR: {e}'}
                return False
        
        # Parallel OCR processing
        success_count = 0
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:  # Ít workers hơn vì API calls
            future_to_pdf = {executor.submit(ocr_single_file, pdf_path): pdf_path 
                            for pdf_path in self.pdf_files}
            
            for future in concurrent.futures.as_completed(future_to_pdf):
                if self.is_cancelled():
                    executor.shutdown(wait=False)
                    return False
                    
                pdf_path = future_to_pdf[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                except Exception as e:
                    filename = os.path.basename(pdf_path)
                    self.log_progress(f"   ❌ Exception OCR {filename}: {e}")
                    self.results[pdf_path] = {'success': False, 'message': f'Exception OCR: {e}'}
        
        # Cập nhật danh sách file thành công
        successful_files = [pdf_path for pdf_path in self.pdf_files 
                          if self.file_data[pdf_path]['text']]
        self.pdf_files = successful_files
        
        self.log_progress(f"✅ Bước 3 hoàn thành: {success_count} file OCR thành công")
        return success_count > 0

    def step4_extract_all_info(self):
        """Bước 4: AI extract TẤT CẢ thông tin (parallel)"""
        self.log_progress("🤖 Bước 4: AI extract tất cả thông tin...")

        # Khởi tạo AI client
        try:
            from rename_pdf import get_ai_client, generate_with_ai
            ai_client = get_ai_client(self.config)
            provider = self.config.get_ai_provider()
            self.log_progress(f"   ✅ {provider} client khởi tạo thành công")
        except Exception as e:
            self.log_progress(f"   ❌ Lỗi khởi tạo AI client: {e}")
            return False

        def extract_single_file_info(pdf_path):
            """Extract thông tin từ một file"""
            try:
                filename = os.path.basename(pdf_path)
                images = self.file_data[pdf_path]['images']
                text = self.file_data[pdf_path]['text']
                start_time = time.time()

                self.log_progress(f"   🔄 Đang extract: {filename}")

                # Lấy ảnh trang đầu
                first_page_image = images[0] if images else None
                if not first_page_image:
                    raise Exception("Không có ảnh trang đầu")

                # Convert ảnh thành bytes
                from io import BytesIO
                buf = BytesIO()
                first_page_image.save(buf, format='PNG')
                first_page_image_data = buf.getvalue()

                # Tạo prompt extraction
                extraction_prompt = """Hãy phân tích ảnh này và trích xuất thông tin theo đúng định dạng sau (nếu không tìm thấy thì để trống sau dấu hai chấm):

Ngày tháng:
Số hiệu:
Tiêu đề:

Yêu cầu:
- Ngày tháng: Tìm ngày tháng trong văn bản (format: dd/mm/yyyy hoặc dd.mm.yyyy)
- Số hiệu: Tìm số văn bản, số quyết định, số thông báo
- Tiêu đề: Lấy nội dung chính của tiêu đề

Hãy trả về đúng 3 dòng theo định dạng trên."""

                try:
                    # Thử extract từ ảnh trước
                    file_info = generate_with_ai(ai_client, extraction_prompt, provider, first_page_image_data)
                except Exception as vision_error:
                    # Fallback: extract từ text
                    self.log_progress(f"   ⚠️ Vision API lỗi cho {filename}, fallback sang text...")
                    first_page_text = text.split('\n\n')[0] if text else ''
                    text_extraction_prompt = f"""Hãy phân tích văn bản sau và trích xuất thông tin theo đúng định dạng:

Ngày tháng:
Số hiệu:
Tiêu đề:

Văn bản:
{first_page_text}"""
                    file_info = generate_with_ai(ai_client, text_extraction_prompt, provider)

                # Parse thông tin
                info_lines = [line for line in file_info.split('\n') if line.strip()]
                date, number, title = "", "", ""

                for line in info_lines:
                    if line.startswith("Ngày tháng:"):
                        date = line.replace("Ngày tháng:", "").strip()
                    elif line.startswith("Số hiệu:"):
                        number = line.replace("Số hiệu:", "").strip()
                    elif line.startswith("Tiêu đề:"):
                        title = line.replace("Tiêu đề:", "").strip()

                # Tạo summary
                summary_prompt = f"Tóm tắt văn bản sau bằng tiếng Việt trong 2-3 câu:\n{text[:2000]}"
                try:
                    summary = generate_with_ai(ai_client, summary_prompt, provider)
                except:
                    summary = "Không thể tóm tắt nội dung"

                extract_time = time.time() - start_time

                # Lưu thông tin đã extract
                self.file_data[pdf_path]['extracted_info'] = {
                    'date': date,
                    'number': number,
                    'title': title
                }
                self.file_data[pdf_path]['summary'] = summary
                self.file_data[pdf_path]['step_times']['extract'] = extract_time

                self.log_progress(f"   ✅ Extract xong: {filename} ({extract_time:.2f}s)")
                return True

            except Exception as e:
                self.log_progress(f"   ❌ Lỗi extract {filename}: {e}")
                self.results[pdf_path] = {'success': False, 'message': f'Lỗi extract: {e}'}
                return False

        # Parallel AI extraction
        success_count = 0
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:  # Ít workers vì API calls
            future_to_pdf = {executor.submit(extract_single_file_info, pdf_path): pdf_path
                            for pdf_path in self.pdf_files}

            for future in concurrent.futures.as_completed(future_to_pdf):
                if self.is_cancelled():
                    executor.shutdown(wait=False)
                    return False

                pdf_path = future_to_pdf[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                except Exception as e:
                    filename = os.path.basename(pdf_path)
                    self.log_progress(f"   ❌ Exception extract {filename}: {e}")
                    self.results[pdf_path] = {'success': False, 'message': f'Exception extract: {e}'}

        # Cập nhật danh sách file thành công
        successful_files = [pdf_path for pdf_path in self.pdf_files
                          if self.file_data[pdf_path]['extracted_info']]
        self.pdf_files = successful_files

        self.log_progress(f"✅ Bước 4 hoàn thành: {success_count} file extract thành công")
        return success_count > 0

    def step5_rename_and_save_all(self):
        """Bước 5: Đổi tên và lưu TẤT CẢ file (sequential để tránh conflict)"""
        self.log_progress("💾 Bước 5: Đổi tên và lưu tất cả file...")

        # Khởi tạo Google Sheets client với proper error handling
        try:
            credentials_file = self.config.get_google_credentials_file()

            # Method 1: Try gspread.service_account() first
            try:
                gc = gspread.service_account(filename=credentials_file)
                self.log_progress(f"   ✅ Google Sheets client (method 1) khởi tạo thành công")
            except Exception as e1:
                self.log_progress(f"   ⚠️ Method 1 failed: {e1}")

                # Method 2: Manual OAuth with explicit scopes
                from google.oauth2 import service_account

                SCOPES = [
                    'https://www.googleapis.com/auth/spreadsheets',
                    'https://www.googleapis.com/auth/drive'
                ]

                creds = service_account.Credentials.from_service_account_file(
                    credentials_file, scopes=SCOPES
                )
                gc = gspread.authorize(creds)
                self.log_progress(f"   ✅ Google Sheets client (method 2) khởi tạo thành công")

            # Lấy sheet name hoặc URL
            sheet_url = self.config.get_google_sheet_url()
            sheet_name = self.config.get_google_sheet_name()

            # Mở sheet bằng URL hoặc name
            if sheet_url and sheet_url.startswith('http'):
                sh = gc.open_by_url(sheet_url)
            elif sheet_name:
                sh = gc.open(sheet_name)
            else:
                raise Exception("Không có Google Sheet URL hoặc name")

            sheet = sh.sheet1

        except Exception as e:
            self.log_progress(f"   ❌ Lỗi khởi tạo Google Sheets: {e}")
            self.log_progress(f"   💡 Hướng dẫn: Kiểm tra credentials và sheet permissions")
            return False

        success_count = 0

        for pdf_path in self.pdf_files:
            if self.is_cancelled():
                return False

            try:
                filename = os.path.basename(pdf_path)
                start_time = time.time()

                self.log_progress(f"   🔄 Đang xử lý: {filename}")

                # Lấy thông tin đã extract
                info = self.file_data[pdf_path]['extracted_info']
                date = info.get('date', '')
                number = info.get('number', '')
                title = info.get('title', '')
                summary = self.file_data[pdf_path]['summary']
                text = self.file_data[pdf_path]['text']

                # Làm sạch thông tin
                date_clean = re.sub(r'[^0-9/.]', '', date)
                number_clean = re.sub(r'[<>:"/\\|?*]', '', number)
                title_clean = re.sub(r'[<>:"/\\|?*]', '', title)

                # Tạo filename mới
                if date_clean and number_clean and title_clean:
                    new_filename = f"{date_clean}.{number_clean}.{title_clean}"
                elif number_clean and title_clean:
                    new_filename = f"{number_clean}.{title_clean}"
                elif title_clean:
                    new_filename = title_clean
                else:
                    new_filename = f"Công văn {datetime.now().strftime('%Y%m%d_%H%M%S')}"

                # Giới hạn độ dài filename
                if len(new_filename) > 200:
                    new_filename = new_filename[:200]

                # Đổi tên file
                new_path = os.path.join(os.path.dirname(pdf_path), f"{new_filename}.pdf")

                # Kiểm tra file đã tồn tại
                counter = 1
                original_new_path = new_path
                while os.path.exists(new_path) and new_path != pdf_path:
                    base_name = new_filename
                    new_path = os.path.join(os.path.dirname(pdf_path), f"{base_name}_{counter}.pdf")
                    counter += 1

                # Đổi tên file
                if new_path != pdf_path:
                    os.rename(pdf_path, new_path)
                    self.log_progress(f"   📝 Đã đổi tên: {os.path.basename(new_path)}")
                else:
                    self.log_progress(f"   📝 Giữ nguyên tên: {filename}")

                # Lưu vào Google Sheets
                row_data = [
                    datetime.now().strftime("%Y-%m-%d %H:%M:%S"),  # Timestamp
                    os.path.basename(new_path),                    # Filename
                    date_clean,                                    # Date
                    number_clean,                                  # Number
                    title_clean,                                   # Title
                    summary,                                       # Summary
                    len(text),                                     # Text length
                    f"{sum(self.file_data[pdf_path]['step_times'].values()):.2f}s"  # Processing time
                ]

                sheet.append_row(row_data)

                save_time = time.time() - start_time
                self.file_data[pdf_path]['step_times']['save'] = save_time
                self.file_data[pdf_path]['new_path'] = new_path
                self.file_data[pdf_path]['new_filename'] = os.path.basename(new_path)

                # Đánh dấu thành công
                self.results[pdf_path] = {
                    'success': True,
                    'message': f'✅ Hoàn thành: {os.path.basename(new_path)}'
                }
                success_count += 1

                self.log_progress(f"   ✅ Hoàn thành: {filename} → {os.path.basename(new_path)} ({save_time:.2f}s)")

            except Exception as e:
                self.log_progress(f"   ❌ Lỗi xử lý {filename}: {e}")
                self.results[pdf_path] = {'success': False, 'message': f'Lỗi save: {e}'}

        self.log_progress(f"✅ Bước 5 hoàn thành: {success_count} file save thành công")
        return success_count > 0

    def get_summary_report(self):
        """Tạo báo cáo tóm tắt"""
        total_files = len(self.results)
        success_count = sum(1 for r in self.results.values() if r['success'])
        failed_count = total_files - success_count

        report = f"""
📊 BÁO CÁO BATCH PROCESSING
{'='*50}
📄 Tổng số file: {total_files}
✅ Thành công: {success_count}
❌ Thất bại: {failed_count}
📈 Tỷ lệ thành công: {(success_count/total_files*100):.1f}%

⏱️ THỜI GIAN XỬ LÝ:
"""

        if success_count > 0:
            # Tính thời gian trung bình cho từng bước
            step_times = defaultdict(list)
            for pdf_path, data in self.file_data.items():
                if self.results[pdf_path]['success']:
                    for step, time_taken in data['step_times'].items():
                        step_times[step].append(time_taken)

            step_names = {
                'convert': 'PDF → Ảnh',
                'ocr': 'OCR',
                'extract': 'AI Extract',
                'save': 'Save & Rename'
            }

            for step, times in step_times.items():
                if times:
                    avg_time = sum(times) / len(times)
                    report += f"📊 {step_names.get(step, step)}: {avg_time:.2f}s trung bình\n"

        if failed_count > 0:
            report += f"\n❌ FILE THẤT BẠI:\n"
            for pdf_path, result in self.results.items():
                if not result['success']:
                    filename = os.path.basename(pdf_path)
                    report += f"   • {filename}: {result['message']}\n"

        return report
