@echo off
echo === DANG KY CONTEXT MENU ===
echo.

:: <PERSON><PERSON><PERSON><PERSON> đế<PERSON> thư mục script
cd /d "%~dp0"

echo Dang kiem tra quyen Administrator...
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo.
    echo LỖI: <PERSON><PERSON><PERSON> quyền Administrator để đăng ký context menu!
    echo H<PERSON>y nhấn chuột phải vào file này và chọn "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo OK - Dang chay voi quyen Administrator
echo.

echo Buoc 1: <PERSON>y dang ky test context menu...
python register_test_context_menu.py unregister
echo.

echo Buoc 2: Dang ky context menu chinh...
python register_main_context_menu.py
echo.

echo Buoc 3: <PERSON><PERSON> tra ket qua...
python register_main_context_menu.py check
echo.

echo === HOAN TAT ===
echo Hay thu nhan chuot phai vao file PDF va chon "Doi ten PDF"
echo.
pause
