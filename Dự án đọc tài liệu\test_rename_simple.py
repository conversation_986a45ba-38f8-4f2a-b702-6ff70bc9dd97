#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script đơn giản để kiểm tra context menu
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def main():
    """Test function đơn giản"""
    try:
        # Tạo một cửa sổ ẩn để hiển thị messagebox
        root = tk.Tk()
        root.withdraw()  # Ẩn cửa sổ chính
        
        if len(sys.argv) > 1:
            pdf_path = sys.argv[1]
            filename = os.path.basename(pdf_path)
            
            message = f"Context menu hoạt động!\n\nFile được chọn:\n{filename}\n\nĐường dẫn đầy đủ:\n{pdf_path}"
            messagebox.showinfo("Test Context Menu", message)
            
            # Ghi log
            script_dir = os.path.dirname(os.path.abspath(__file__))
            log_file = os.path.join(script_dir, 'context_menu_test.log')
            
            with open(log_file, 'a', encoding='utf-8') as f:
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"{timestamp} - Context menu called with: {pdf_path}\n")
            
        else:
            messagebox.showerror("Lỗi", "Không có file PDF được chọn!")
        
        root.destroy()
        
    except Exception as e:
        # Fallback nếu GUI không hoạt động
        print(f"Context menu test - Error: {e}")
        if len(sys.argv) > 1:
            print(f"PDF file: {sys.argv[1]}")
        else:
            print("No PDF file provided")

if __name__ == "__main__":
    main()
