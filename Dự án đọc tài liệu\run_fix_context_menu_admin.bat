@echo off
:: <PERSON><PERSON><PERSON> <PERSON><PERSON> quyền Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo <PERSON><PERSON> chạy vớ<PERSON> quyền Administrator...
    cd /d "%~dp0"
    echo.
    echo === KIỂM TRA TRƯỚC KHI SỬA ===
    python fix_context_menu.py check
    echo.
    echo === ĐĂNG KÝ CONTEXT MENU ===
    python fix_context_menu.py
    echo.
    echo === KIỂM TRA SAU KHI SỬA ===
    python fix_context_menu.py check
    echo.
    echo Hoàn tất! H<PERSON>y thử nhấn chuột phải vào file PDF.
    pause
) else (
    echo Cần quyền Administrator để đăng ký context menu.
    echo Đang khởi động lại với quyền Administrator...
    powershell -WindowStyle Hidden -Command "Start-Process cmd -ArgumentList '/c \"%~f0\"' -Verb RunAs"
)
