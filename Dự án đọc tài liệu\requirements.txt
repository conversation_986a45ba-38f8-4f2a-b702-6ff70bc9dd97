PySide6>=6.8.0
google-cloud-vision>=3.8.0
python-docx>=1.1.2
pdf2image>=1.17.0
pytesseract>=0.3.13
Pillow>=10.4.0
google-generativeai>=0.8.3
python-dotenv>=1.0.1
openai>=1.54.4
pdf2docx>=0.5.8
gspread>=6.1.2
google-auth>=2.35.0
PyQt5>=5.15.11
groq>=0.11.0
pywin32>=310
google-oauth2-tool>=0.0.3
google-auth-oauthlib>=1.2.1
requests>=2.32.3
urllib3>=2.2.3

# Additional packages for changes tracking and progress dialog functionality
# Note: tkinter is usually built-in with Python
# If tkinter is missing, install it via system package manager:
# Ubuntu/Debian: sudo apt-get install python3-tk
# CentOS/RHEL: sudo yum install tkinter
# Windows: Should be included with Python installation

# Threading support (built-in)
# datetime support (built-in)