#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script đơn giản để đăng ký context menu
"""

import os
import sys
import winreg

def find_python():
    """Tìm đường dẫn Python"""
    possible_paths = [
        r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
        r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
        r"C:\Python313\pythonw.exe",
        r"C:\Python312\pythonw.exe",
        r"C:\Python311\pythonw.exe",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # Fallback
    return "pythonw.exe"

def register_context_menu():
    """Đăng ký context menu"""
    try:
        # Đường dẫn
        script_dir = os.path.dirname(os.path.abspath(__file__))
        python_path = find_python()
        script_path = os.path.join(script_dir, "rename_pdf.py")
        
        print(f"Python: {python_path}")
        print(f"Script: {script_path}")
        
        # Kiểm tra file
        if not os.path.exists(script_path):
            print(f"LỖI: Không tìm thấy {script_path}")
            return False
        
        # Xóa key cũ
        try:
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF")
            print("Đã xóa context menu cũ")
        except:
            pass
        
        # Tạo key mới
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF") as key:
            winreg.SetValue(key, "", winreg.REG_SZ, "Đổi tên PDF")
        
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF\command") as key:
            command = f'"{python_path}" "{script_path}" "%1"'
            winreg.SetValue(key, "", winreg.REG_SZ, command)
            print(f"Command: {command}")
        
        print("✓ Đăng ký thành công!")
        return True
        
    except PermissionError:
        print("✗ Cần quyền Administrator")
        return False
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def check_context_menu():
    """Kiểm tra context menu"""
    try:
        with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF\command") as key:
            command = winreg.QueryValue(key, "")
            print(f"✓ Context menu đã đăng ký: {command}")
            return True
    except:
        print("✗ Context menu chưa được đăng ký")
        return False

def main():
    print("=== ĐĂNG KÝ CONTEXT MENU ĐƠN GIẢN ===")
    
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        check_context_menu()
    else:
        if register_context_menu():
            print("\nKiểm tra:")
            check_context_menu()

if __name__ == "__main__":
    main()
