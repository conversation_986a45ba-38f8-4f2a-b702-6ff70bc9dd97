import os
import sys
import json
from pathlib import Path
import datetime
import copy

# Import Windows registry only on Windows
try:
    import winreg
    WINDOWS_AVAILABLE = True
except ImportError:
    WINDOWS_AVAILABLE = False
    print("Warning: winreg not available (not on Windows)")

# Import tkinter with error handling
try:
    import tkinter as tk
    from tkinter import messagebox, filedialog, ttk
    TKINTER_AVAILABLE = True
except ImportError as e:
    TKINTER_AVAILABLE = False
    print(f"Warning: tkinter not available: {e}")
    print("GUI functionality will be disabled")

    # Create dummy classes for when tkinter is not available
    class DummyTk:
        def __init__(self):
            raise ImportError("tkinter not available")

    tk = type('tk', (), {
        'Tk': DummyTk,
        'Frame': DummyTk,
        'Label': DummyTk,
        'Entry': DummyTk,
        'Button': DummyTk,
        'StringVar': DummyTk,
        'BooleanVar': DummyTk,
        'Checkbutton': DummyTk,
        'Canvas': DummyTk,
        'Text': DummyTk,
        'Scrollbar': DummyTk,
        'LabelFrame': DummyTk,
        'LEFT': 'left',
        'RIGHT': 'right',
        'BOTH': 'both',
        'X': 'x',
        'Y': 'y',
        'END': 'end',
        'WORD': 'word',
        'W': 'w'
    })()

    messagebox = type('messagebox', (), {
        'showinfo': lambda *args: print(f"Info: {args}"),
        'showerror': lambda *args: print(f"Error: {args}"),
        'askyesno': lambda *args: True
    })()

    filedialog = type('filedialog', (), {
        'askopenfilename': lambda *args, **kwargs: ""
    })()

    ttk = type('ttk', (), {
        'Notebook': DummyTk,
        'Frame': DummyTk,
        'Scrollbar': DummyTk
    })()

class PDFContextMenuManager:
    def __init__(self):
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        self.config_file = os.path.join(self.script_dir, 'context_menu_config.json')
        self.changes_log_file = os.path.join(self.script_dir, 'context_menu_changes.log')

        # Lưu trữ config ban đầu để so sánh thay đổi
        self.original_config = None

        self.load_config()

        # Lưu config ban đầu để theo dõi thay đổi
        self.original_config = copy.deepcopy(self.config)
        
    def load_config(self):
        """Load cấu hình context menu"""
        # Sử dụng pythonw.exe để ẩn terminal
        python_exe = sys.executable
        if python_exe.endswith('python.exe'):
            pythonw_exe = python_exe.replace('python.exe', 'pythonw.exe')
            if os.path.exists(pythonw_exe):
                python_exe = pythonw_exe

        default_config = {
            'python_path': python_exe,
            'rename_script_path': os.path.join(self.script_dir, 'rename_pdf.pyw'),
            'vbs_script_path': os.path.join(self.script_dir, 'run_rename_pdf_hidden.vbs'),
            'menu_text': 'Đổi tên PDF và lưu Google Sheet',
            'icon_path': '',
            'use_vbs': True  # Sử dụng VBS để ẩn terminal
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    default_config.update(loaded_config)
            except Exception as e:
                print(f"Lỗi khi đọc config: {e}")
        
        self.config = default_config
        self.save_config()
    
    def detect_changes(self):
        """Phát hiện thay đổi trong config"""
        changes = []

        # Kiểm tra nếu original_config chưa được khởi tạo
        if self.original_config is None:
            return []

        def compare_dict(original, current, path=""):
            if original is None or current is None:
                return

            for key in set(list(original.keys()) + list(current.keys())):
                current_path = f"{path}.{key}" if path else key

                if key not in original:
                    changes.append(f"ADDED: {current_path} = {current.get(key)}")
                elif key not in current:
                    changes.append(f"REMOVED: {current_path} = {original.get(key)}")
                elif original[key] != current[key]:
                    changes.append(f"CHANGED: {current_path} = {original[key]} -> {current[key]}")

        compare_dict(self.original_config, self.config)
        return changes

    def log_changes(self, changes):
        """Ghi log thay đổi"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        with open(self.changes_log_file, 'a', encoding='utf-8') as f:
            f.write(f"\n=== CONTEXT MENU CONFIG CHANGES - {timestamp} ===\n")
            f.write("⚠️  QUAN TRỌNG: Thay đổi context menu có thể ảnh hưởng đến menu chuột phải PDF\n")
            for change in changes:
                f.write(f"{change}\n")
            f.write("💡 Lưu ý: Có thể cần đăng ký lại context menu sau khi thay đổi\n")
            f.write("=" * 60 + "\n")

    def get_changes_log(self):
        """Đọc log thay đổi"""
        if os.path.exists(self.changes_log_file):
            try:
                with open(self.changes_log_file, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                return f"Lỗi đọc log: {str(e)}"
        return "Chưa có thay đổi nào được ghi lại."

    def save_config(self):
        """Lưu cấu hình context menu và ghi log thay đổi"""
        # Kiểm tra thay đổi trước khi lưu (chỉ nếu original_config đã được khởi tạo)
        changes = []
        if self.original_config is not None:
            changes = self.detect_changes()

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)

            # Ghi log thay đổi nếu có
            if changes:
                self.log_changes(changes)

            # Cập nhật config gốc
            self.original_config = copy.deepcopy(self.config)

            return changes

        except Exception as e:
            print(f"Lỗi khi lưu config: {e}")
            return []
    
    def register_context_menu(self):
        """Đăng ký context menu cho file PDF"""
        if not WINDOWS_AVAILABLE:
            return False, "Chức năng này chỉ hoạt động trên Windows"

        try:
            # Tạo key cho PDF files
            pdf_key_path = r"*\shell\RenamePDF"

            # Tạo key chính
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path) as key:
                winreg.SetValue(key, "", winreg.REG_SZ, self.config['menu_text'])

                # Thêm icon nếu có
                if self.config['icon_path'] and os.path.exists(self.config['icon_path']):
                    winreg.SetValueEx(key, "Icon", 0, winreg.REG_SZ, self.config['icon_path'])

            # Tạo command key
            command_key_path = pdf_key_path + r"\command"
            with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, command_key_path) as key:
                if self.config.get('use_vbs', False) and os.path.exists(self.config.get('vbs_script_path', '')):
                    # Sử dụng VBS để ẩn terminal
                    command = f'wscript.exe "{self.config["vbs_script_path"]}" "%1"'
                else:
                    # Sử dụng pythonw.exe để ẩn terminal
                    command = f'"{self.config["python_path"]}" "{self.config["rename_script_path"]}" "%1"'
                winreg.SetValue(key, "", winreg.REG_SZ, command)

            return True, "Đã đăng ký context menu thành công!"

        except PermissionError:
            return False, "Cần quyền Administrator để đăng ký context menu. Vui lòng chạy với quyền Administrator."
        except Exception as e:
            return False, f"Lỗi khi đăng ký context menu: {str(e)}"
    
    def unregister_context_menu(self):
        """Hủy đăng ký context menu"""
        if not WINDOWS_AVAILABLE:
            return False, "Chức năng này chỉ hoạt động trên Windows"

        try:
            pdf_key_path = r"*\shell\RenamePDF"
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path + r"\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path)
            return True, "Đã hủy đăng ký context menu thành công!"
        except FileNotFoundError:
            return False, "Context menu chưa được đăng ký."
        except PermissionError:
            return False, "Cần quyền Administrator để hủy đăng ký context menu."
        except Exception as e:
            return False, f"Lỗi khi hủy đăng ký context menu: {str(e)}"
    
    def is_registered(self):
        """Kiểm tra xem context menu đã được đăng ký chưa"""
        if not WINDOWS_AVAILABLE:
            return False

        try:
            with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF"):
                return True
        except FileNotFoundError:
            return False
        except Exception:
            return False

class ContextMenuGUI:
    def __init__(self):
        if not TKINTER_AVAILABLE:
            raise ImportError("tkinter not available - GUI cannot be started")

        self.manager = PDFContextMenuManager()
        self.root = tk.Tk()
        self.root.title("PDF Context Menu Manager")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        self.setup_ui()
        
    def setup_ui(self):
        # Frame chính
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Tiêu đề
        title_label = tk.Label(main_frame, text="Quản lý Context Menu cho PDF",
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # Tạo notebook cho tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Tab 1: Cấu hình chính
        self.create_main_config_tab()

        # Tab 2: Lịch sử thay đổi
        self.create_changes_log_tab()

        # Buttons chung
        self.create_action_buttons(main_frame)

    def create_main_config_tab(self):
        """Tạo tab cấu hình chính"""
        config_tab = ttk.Frame(self.notebook)
        self.notebook.add(config_tab, text="⚙️ Cấu hình")

        # Tạo scrollable frame
        canvas = tk.Canvas(config_tab)
        scrollbar = ttk.Scrollbar(config_tab, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Nội dung tab cấu hình
        self.setup_config_content(scrollable_frame)

    def setup_config_content(self, parent):
        """Thiết lập nội dung tab cấu hình"""
        # Trạng thái hiện tại
        status_frame = tk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 20))

        tk.Label(status_frame, text="Trạng thái hiện tại:", font=("Arial", 10, "bold")).pack(anchor=tk.W)
        self.status_label = tk.Label(status_frame, text="", fg="blue")
        self.status_label.pack(anchor=tk.W)

        # Cấu hình
        config_frame = tk.LabelFrame(parent, text="🔧 Cấu hình Context Menu", padx=10, pady=10)
        config_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Python path
        python_frame = tk.Frame(config_frame)
        python_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(python_frame, text="Đường dẫn Python:").pack(anchor=tk.W)
        python_entry_frame = tk.Frame(python_frame)
        python_entry_frame.pack(fill=tk.X, pady=(5, 0))
        self.python_path_var = tk.StringVar(value=self.manager.config['python_path'])
        self.python_path_entry = tk.Entry(python_entry_frame, textvariable=self.python_path_var)
        self.python_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(python_entry_frame, text="Browse", 
                 command=self.browse_python).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Script path
        script_frame = tk.Frame(config_frame)
        script_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(script_frame, text="Đường dẫn Script:").pack(anchor=tk.W)
        script_entry_frame = tk.Frame(script_frame)
        script_entry_frame.pack(fill=tk.X, pady=(5, 0))
        self.script_path_var = tk.StringVar(value=self.manager.config['rename_script_path'])
        self.script_path_entry = tk.Entry(script_entry_frame, textvariable=self.script_path_var)
        self.script_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(script_entry_frame, text="Browse", 
                 command=self.browse_script).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Menu text
        menu_frame = tk.Frame(config_frame)
        menu_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(menu_frame, text="Tên hiển thị trong menu:").pack(anchor=tk.W)
        self.menu_text_var = tk.StringVar(value=self.manager.config['menu_text'])
        self.menu_text_entry = tk.Entry(menu_frame, textvariable=self.menu_text_var)
        self.menu_text_entry.pack(fill=tk.X, pady=(5, 0))
        
        # Icon path (optional)
        icon_frame = tk.Frame(config_frame)
        icon_frame.pack(fill=tk.X, pady=(0, 10))
        tk.Label(icon_frame, text="Đường dẫn Icon (tùy chọn):").pack(anchor=tk.W)
        icon_entry_frame = tk.Frame(icon_frame)
        icon_entry_frame.pack(fill=tk.X, pady=(5, 0))
        self.icon_path_var = tk.StringVar(value=self.manager.config.get('icon_path', ''))
        self.icon_path_entry = tk.Entry(icon_entry_frame, textvariable=self.icon_path_var)
        self.icon_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        tk.Button(icon_entry_frame, text="Browse",
                 command=self.browse_icon).pack(side=tk.RIGHT, padx=(5, 0))

        # Hide terminal option
        hide_frame = tk.Frame(config_frame)
        hide_frame.pack(fill=tk.X, pady=(0, 10))
        self.use_vbs_var = tk.BooleanVar(value=self.manager.config.get('use_vbs', True))
        self.use_vbs_checkbox = tk.Checkbutton(hide_frame,
                                              text="Ẩn cửa sổ terminal khi chạy (khuyến nghị)",
                                              variable=self.use_vbs_var)
        self.use_vbs_checkbox.pack(anchor=tk.W)

        # Hướng dẫn
        help_frame = tk.LabelFrame(parent, text="📖 Hướng dẫn", padx=10, pady=10)
        help_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        help_text = """1. Cấu hình đường dẫn Python và script rename_pdf.py
2. Nhập tên hiển thị cho menu context
3. Tùy chọn: Chọn icon cho menu
4. Nhấn "💾 Lưu cấu hình" để lưu các thay đổi
5. Nhấn "✅ Đăng ký Context Menu" để thêm menu vào chuột phải
6. Sau khi đăng ký, bạn có thể nhấn chuột phải vào file PDF và chọn menu đã tạo

⚠️ Lưu ý: Cần quyền Administrator để đăng ký/hủy đăng ký context menu.
📝 Tất cả thay đổi sẽ được ghi log tự động."""

        help_label = tk.Label(help_frame, text=help_text, justify=tk.LEFT, wraplength=600)
        help_label.pack(anchor=tk.W)

    def create_changes_log_tab(self):
        """Tạo tab lịch sử thay đổi"""
        log_tab = ttk.Frame(self.notebook)
        self.notebook.add(log_tab, text="📝 Lịch sử thay đổi")

        # Frame chứa log
        log_frame = tk.Frame(log_tab, padx=10, pady=10)
        log_frame.pack(fill=tk.BOTH, expand=True)

        # Label tiêu đề
        log_title = tk.Label(log_frame, text="📄 Lịch sử thay đổi Context Menu Config",
                            font=("Arial", 12, "bold"))
        log_title.pack(pady=(0, 10))

        # Text widget với scrollbar
        text_frame = tk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 9))
        log_scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")

        # Button làm mới log
        refresh_button = tk.Button(log_frame, text="🔄 Làm mới log",
                                  command=self.refresh_log, bg="lightblue")
        refresh_button.pack(pady=(10, 0))

        # Load log ban đầu
        self.refresh_log()

    def create_action_buttons(self, parent):
        """Tạo các nút hành động"""
        button_frame = tk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Button(button_frame, text="💾 Lưu cấu hình",
                 command=self.save_config, bg="lightblue",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=(0, 10))
        tk.Button(button_frame, text="✅ Đăng ký Context Menu",
                 command=self.register_menu, bg="lightgreen",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=(0, 10))
        tk.Button(button_frame, text="❌ Hủy đăng ký",
                 command=self.unregister_menu, bg="lightcoral",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=(0, 10))
        tk.Button(button_frame, text="🔍 Kiểm tra trạng thái",
                 command=self.check_status, bg="lightyellow").pack(side=tk.LEFT)

        # Kiểm tra trạng thái ban đầu
        self.check_status()
    
    def browse_python(self):
        """Chọn file Python executable"""
        file_path = filedialog.askopenfilename(
            title="Chọn Python executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            self.python_path_var.set(file_path)
    
    def browse_script(self):
        """Chọn file script Python"""
        file_path = filedialog.askopenfilename(
            title="Chọn rename_pdf.py",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        if file_path:
            self.script_path_var.set(file_path)
    
    def browse_icon(self):
        """Chọn file icon"""
        file_path = filedialog.askopenfilename(
            title="Chọn file icon",
            filetypes=[("Icon files", "*.ico"), ("Image files", "*.png;*.jpg;*.bmp"), ("All files", "*.*")]
        )
        if file_path:
            self.icon_path_var.set(file_path)
    
    def refresh_log(self):
        """Làm mới nội dung log"""
        log_content = self.manager.get_changes_log()
        self.log_text.delete(1.0, tk.END)
        self.log_text.insert(1.0, log_content)

        # Scroll xuống cuối
        self.log_text.see(tk.END)

    def show_changes_summary(self, changes):
        """Hiển thị tóm tắt thay đổi"""
        if not changes:
            return

        message = "📝 Tóm tắt thay đổi đã lưu:\n\n"

        for change in changes[:10]:  # Chỉ hiển thị 10 thay đổi đầu
            message += f"  • {change}\n"

        if len(changes) > 10:
            message += f"  ... và {len(changes) - 10} thay đổi khác\n"

        message += "\n⚠️ Lưu ý: Có thể cần đăng ký lại context menu sau khi thay đổi!"

        messagebox.showinfo("Thay đổi đã lưu", message)

    def save_config(self):
        """Lưu cấu hình với theo dõi thay đổi"""
        self.manager.config['python_path'] = self.python_path_var.get()
        self.manager.config['rename_script_path'] = self.script_path_var.get()
        self.manager.config['menu_text'] = self.menu_text_var.get()
        self.manager.config['icon_path'] = self.icon_path_var.get()
        self.manager.config['use_vbs'] = self.use_vbs_var.get()

        # Lưu và lấy thay đổi
        changes = self.manager.save_config()

        # Hiển thị thông báo với tóm tắt thay đổi
        if changes:
            self.show_changes_summary(changes)
            # Làm mới log
            self.refresh_log()
        else:
            messagebox.showinfo("✅ Thành công", "Đã lưu cấu hình (không có thay đổi)")
    
    def register_menu(self):
        """Đăng ký context menu"""
        # Lưu cấu hình trước khi đăng ký
        changes = self.manager.detect_changes()
        if changes:
            self.save_config()

        success, message = self.manager.register_context_menu()
        if success:
            messagebox.showinfo("✅ Thành công", message)
            # Ghi log hành động đăng ký
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open(self.manager.changes_log_file, 'a', encoding='utf-8') as f:
                f.write(f"\n=== CONTEXT MENU REGISTERED - {timestamp} ===\n")
                f.write("✅ Context menu đã được đăng ký thành công\n")
                f.write("=" * 60 + "\n")
            self.refresh_log()
        else:
            messagebox.showerror("❌ Lỗi", message)

        self.check_status()
    
    def unregister_menu(self):
        """Hủy đăng ký context menu"""
        if messagebox.askyesno("⚠️ Xác nhận", "Bạn có chắc muốn hủy đăng ký context menu?"):
            success, message = self.manager.unregister_context_menu()
            if success:
                messagebox.showinfo("✅ Thành công", message)
                # Ghi log hành động hủy đăng ký
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                with open(self.manager.changes_log_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n=== CONTEXT MENU UNREGISTERED - {timestamp} ===\n")
                    f.write("❌ Context menu đã được hủy đăng ký\n")
                    f.write("=" * 60 + "\n")
                self.refresh_log()
            else:
                messagebox.showerror("❌ Lỗi", message)

            self.check_status()
    
    def check_status(self):
        """Kiểm tra trạng thái đăng ký"""
        if self.manager.is_registered():
            self.status_label.config(text="✅ Context menu đã được đăng ký", fg="green")
        else:
            self.status_label.config(text="❌ Context menu chưa được đăng ký", fg="red")
    
    def run(self):
        """Chạy GUI"""
        self.root.mainloop()

def main():
    """Hàm main để chạy từ command line hoặc GUI"""
    if len(sys.argv) > 1:
        # Chạy từ command line
        manager = PDFContextMenuManager()

        if sys.argv[1] == "register":
            success, message = manager.register_context_menu()
            print(message)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "unregister":
            success, message = manager.unregister_context_menu()
            print(message)
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "status":
            if manager.is_registered():
                print("Context menu đã được đăng ký")
            else:
                print("Context menu chưa được đăng ký")
            sys.exit(0)
        elif sys.argv[1] == "test":
            # Test mode - không cần GUI
            print("🧪 Testing PDFContextMenuManager...")
            try:
                manager = PDFContextMenuManager()
                print("✅ Manager created successfully")

                # Test change detection
                original_text = manager.config['menu_text']
                manager.config['menu_text'] = 'Test Menu'
                changes = manager.detect_changes()
                print(f"📝 Changes detected: {len(changes)}")

                # Restore original
                manager.config['menu_text'] = original_text
                manager.save_config()

                print("✅ Test completed successfully")
            except Exception as e:
                print(f"❌ Test failed: {e}")
                sys.exit(1)
            sys.exit(0)
    else:
        # Chạy GUI
        if not TKINTER_AVAILABLE:
            print("❌ GUI không khả dụng - tkinter không được cài đặt")
            print("💡 Sử dụng command line thay thế:")
            print("   python register_pdf_context_menu.py register")
            print("   python register_pdf_context_menu.py unregister")
            print("   python register_pdf_context_menu.py status")
            print("   python register_pdf_context_menu.py test")
            sys.exit(1)

        try:
            app = ContextMenuGUI()
            app.run()
        except Exception as e:
            print(f"❌ Lỗi khởi tạo GUI: {e}")
            print("💡 Thử chạy với command line thay thế")
            sys.exit(1)

if __name__ == "__main__":
    main()
