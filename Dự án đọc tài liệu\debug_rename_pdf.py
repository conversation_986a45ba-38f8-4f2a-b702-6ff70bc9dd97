#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug version của rename_pdf.py
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from datetime import datetime
import traceback

def debug_log(message):
    """Ghi debug log"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        log_file = os.path.join(script_dir, 'debug_rename_pdf.log')
        
        with open(log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"{timestamp} - {message}\n")
    except:
        pass

def show_message(title, message):
    """Hiển thị thông báo"""
    try:
        root = tk.Tk()
        root.withdraw()
        if "Lỗi" in title:
            messagebox.showerror(title, message)
        else:
            messagebox.showinfo(title, message)
        root.destroy()
    except Exception as e:
        debug_log(f"Error showing message: {e}")

def main():
    debug_log("=== DEBUG RENAME PDF STARTED ===")
    
    try:
        debug_log(f"Python version: {sys.version}")
        debug_log(f"Script path: {__file__}")
        debug_log(f"Working directory: {os.getcwd()}")
        debug_log(f"Arguments: {sys.argv}")
        
        if len(sys.argv) > 1:
            pdf_path = sys.argv[1]
            debug_log(f"PDF path: {pdf_path}")
            
            # Kiểm tra file
            if not os.path.exists(pdf_path):
                error_msg = f"File không tồn tại: {pdf_path}"
                debug_log(f"ERROR: {error_msg}")
                show_message("Lỗi", error_msg)
                return
            
            if not pdf_path.lower().endswith('.pdf'):
                error_msg = f"File không phải PDF: {pdf_path}"
                debug_log(f"ERROR: {error_msg}")
                show_message("Lỗi", error_msg)
                return
            
            debug_log("File PDF hợp lệ")
            
            # Kiểm tra config
            script_dir = os.path.dirname(os.path.abspath(__file__))
            config_file = os.path.join(script_dir, 'config.json')
            debug_log(f"Config file: {config_file}")
            debug_log(f"Config exists: {os.path.exists(config_file)}")
            
            if not os.path.exists(config_file):
                error_msg = "Không tìm thấy file config.json. Vui lòng chạy config_manager.py trước."
                debug_log(f"ERROR: {error_msg}")
                show_message("Lỗi", error_msg)
                return
            
            debug_log("Config file OK")
            
            # Thử import các module cần thiết
            try:
                debug_log("Testing imports...")
                import google.generativeai as genai
                debug_log("✓ google.generativeai imported")
                
                import openai
                debug_log("✓ openai imported")
                
                from google.cloud import vision
                debug_log("✓ google.cloud.vision imported")
                
                from pdf2image import convert_from_path
                debug_log("✓ pdf2image imported")
                
                import gspread
                debug_log("✓ gspread imported")
                
                debug_log("All imports successful")
                
            except Exception as e:
                error_msg = f"Lỗi import module: {str(e)}"
                debug_log(f"ERROR: {error_msg}")
                show_message("Lỗi", error_msg)
                return
            
            # Nếu đến đây thì OK
            filename = os.path.basename(pdf_path)
            success_msg = f"DEBUG: Tất cả kiểm tra đều OK!\n\nFile: {filename}\n\nCó thể bắt đầu xử lý PDF."
            debug_log("All checks passed - ready to process")
            show_message("Debug OK", success_msg)
            
        else:
            error_msg = "Không có file PDF được chọn"
            debug_log(f"ERROR: {error_msg}")
            show_message("Lỗi", error_msg)
    
    except Exception as e:
        error_msg = f"Lỗi không mong muốn: {str(e)}"
        debug_log(f"EXCEPTION: {error_msg}")
        debug_log(f"Traceback: {traceback.format_exc()}")
        show_message("Lỗi", error_msg)
    
    debug_log("=== DEBUG RENAME PDF ENDED ===")

if __name__ == "__main__":
    main()
