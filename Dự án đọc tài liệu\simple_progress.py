#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Progress Dialog - Giao <PERSON>n progress đơn gi<PERSON>n cho xử lý PDF
"""

import tkinter as tk
from tkinter import ttk
import threading
import time

class SimpleProgressDialog:
    def __init__(self, title="Đang xử lý..."):
        self.root = tk.Tk()
        self.root.title(title)
        self.root.geometry("600x400")
        self.root.resizable(True, True)
        self.root.minsize(500, 350)
        
        # Set window properties
        self.root.attributes('-topmost', True)  # Always on top
        self.root.withdraw()  # Hide initially
        
        # Variables
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar()
        self.is_cancelled = False
        self.is_completed = False
        self.work_thread = None
        
        self.setup_ui()
        self.center_window()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔄 Đang xử lý PDF...", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Progress bar
        self.progress_bar = ttk.Progressbar(main_frame, 
                                           variable=self.progress_var,
                                           maximum=100,
                                           length=400,
                                           mode='determinate')
        self.progress_bar.pack(pady=(0, 10))
        
        # Progress percentage
        self.progress_label = ttk.Label(main_frame, text="0%", 
                                       font=("Arial", 10, "bold"))
        self.progress_label.pack()
        
        # Status
        status_frame = ttk.LabelFrame(main_frame, text="Trạng thái", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 10))

        # Create scrollable text widget for status
        status_text_frame = ttk.Frame(status_frame)
        status_text_frame.pack(fill=tk.BOTH, expand=True)

        self.status_text = tk.Text(status_text_frame, height=6, wrap=tk.WORD,
                                  font=("Arial", 9), state=tk.DISABLED)

        # Scrollbar for status text
        status_scrollbar = ttk.Scrollbar(status_text_frame, orient=tk.VERTICAL,
                                        command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)

        self.status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        status_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        self.cancel_button = ttk.Button(button_frame, text="❌ Hủy", 
                                       command=self.cancel)
        self.cancel_button.pack(side=tk.RIGHT, padx=(10, 0))
        
        self.close_button = ttk.Button(button_frame, text="✅ Đóng", 
                                      command=self.close,
                                      state=tk.DISABLED)
        self.close_button.pack(side=tk.RIGHT)
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.cancel)
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def update_progress(self, percentage, status=""):
        """Update progress bar and status"""
        def update():
            self.progress_var.set(percentage)
            self.progress_label.config(text=f"{percentage:.1f}%")

            if status:
                # Update text widget instead of label
                self.status_text.config(state=tk.NORMAL)
                self.status_text.delete(1.0, tk.END)
                self.status_text.insert(tk.END, status)
                self.status_text.config(state=tk.DISABLED)
                # Auto scroll to bottom
                self.status_text.see(tk.END)

            self.root.update()

        if threading.current_thread() == threading.main_thread():
            update()
        else:
            self.root.after(0, update)
            
    def set_completed(self, success=True, message=""):
        """Mark as completed"""
        def complete():
            self.is_completed = True
            if success:
                self.progress_var.set(100)
                self.progress_label.config(text="100%")
                self.root.title("✅ Hoàn thành")
            else:
                self.root.title("❌ Lỗi")

            # Update status text
            if message:
                self.status_text.config(state=tk.NORMAL)
                self.status_text.delete(1.0, tk.END)
                self.status_text.insert(tk.END, message or ("✅ Hoàn thành!" if success else "❌ Có lỗi xảy ra!"))
                self.status_text.config(state=tk.DISABLED)
                self.status_text.see(tk.END)

            self.cancel_button.config(state=tk.DISABLED)
            self.close_button.config(state=tk.NORMAL)
            self.root.update()

        if threading.current_thread() == threading.main_thread():
            complete()
        else:
            self.root.after(0, complete)
            
    def cancel(self):
        """Cancel the operation"""
        self.is_cancelled = True
        # Update status text
        self.status_text.config(state=tk.NORMAL)
        self.status_text.delete(1.0, tk.END)
        self.status_text.insert(tk.END, "⚠️ Đang hủy...")
        self.status_text.config(state=tk.DISABLED)
        self.root.title("⚠️ Đang hủy")
        self.close()
        
    def close(self):
        """Close the dialog"""
        try:
            self.root.quit()
            self.root.destroy()
        except:
            pass
            
    def show(self):
        """Show the dialog"""
        self.center_window()
        self.root.deiconify()
        self.root.lift()
        self.root.focus_force()

    def show_and_run(self, work_function):
        """Show dialog and run work function in thread"""
        self.show()
        
        def run_work():
            try:
                work_function(self)
            except Exception as e:
                self.set_completed(False, f"Lỗi: {str(e)}")
        
        # Start work in background thread
        self.work_thread = threading.Thread(target=run_work, daemon=True)
        self.work_thread.start()
        
        # Start main loop
        try:
            self.root.mainloop()
        except:
            pass

class SimpleProgressManager:
    """Simple progress manager for easy use"""
    
    def __init__(self, title="Đang xử lý...", dialog=None):
        if dialog:
            self.dialog = dialog
        else:
            self.dialog = SimpleProgressDialog(title)
        self.current_step = 0
        self.total_steps = 0
        self.step_weights = []
        
    def set_steps(self, steps):
        """Set danh sách các bước với trọng số
        steps: list of (step_name, weight) tuples
        """
        self.steps = steps
        self.total_steps = len(steps)
        self.step_weights = [weight for _, weight in steps]
        self.total_weight = sum(self.step_weights)
        self.current_step = 0
        
    def start_step(self, step_index, detail=""):
        """Bắt đầu một bước"""
        if step_index < len(self.steps):
            step_name, _ = self.steps[step_index]
            self.current_step = step_index
            
            # Calculate progress up to this step
            progress = sum(self.step_weights[:step_index]) / self.total_weight * 100
            
            status = f"Bước {step_index + 1}/{self.total_steps}: {step_name}"
            if detail:
                status += f" - {detail}"
                
            self.dialog.update_progress(progress, status)
            
    def update_step_progress(self, step_progress, detail=""):
        """Update progress trong bước hiện tại"""
        if self.current_step < len(self.steps):
            # Progress của các bước đã hoàn thành
            completed_progress = sum(self.step_weights[:self.current_step]) / self.total_weight * 100
            
            # Progress của bước hiện tại
            current_step_weight = self.step_weights[self.current_step]
            current_step_progress = (step_progress / 100) * (current_step_weight / self.total_weight * 100)
            
            total_progress = completed_progress + current_step_progress
            
            step_name, _ = self.steps[self.current_step]
            status = f"Bước {self.current_step + 1}/{self.total_steps}: {step_name} ({step_progress:.1f}%)"
            if detail:
                status += f" - {detail}"
                
            self.dialog.update_progress(total_progress, status)
            
    def complete_step(self, detail=""):
        """Hoàn thành bước hiện tại"""
        if self.current_step < len(self.steps):
            step_name, _ = self.steps[self.current_step]
            
            # Progress sau khi hoàn thành bước này
            progress = sum(self.step_weights[:self.current_step + 1]) / self.total_weight * 100
            
            status = f"✅ Hoàn thành: {step_name}"
            if detail:
                status += f" - {detail}"
                
            self.dialog.update_progress(progress, status)
            
    def set_completed(self, success=True, message=""):
        """Hoàn thành toàn bộ quá trình"""
        self.dialog.set_completed(success, message)
        
    def is_cancelled(self):
        """Kiểm tra xem có bị hủy không"""
        return self.dialog.is_cancelled
        
    def show(self):
        """Hiển thị dialog"""
        self.dialog.show()

# Test function
def test_simple_progress():
    """Test simple progress dialog"""
    def simulate_work(dialog):
        manager = SimpleProgressManager("Test Simple Progress", dialog)
        
        # Define steps
        steps = [
            ("Khởi tạo", 10),
            ("Chuyển đổi PDF", 30),
            ("OCR văn bản", 40),
            ("Trích xuất thông tin", 15),
            ("Lưu kết quả", 5)
        ]
        
        manager.set_steps(steps)
        
        try:
            for i, (step_name, _) in enumerate(steps):
                if manager.is_cancelled():
                    break

                manager.start_step(i, f"Bắt đầu {step_name}...")

                # Simulate work with sub-progress
                for j in range(10):
                    if manager.is_cancelled():
                        break

                    time.sleep(0.1)
                    sub_progress = (j + 1) * 10
                    manager.update_step_progress(sub_progress, f"Đang xử lý...")

                manager.complete_step()
                
            if not manager.is_cancelled():
                manager.set_completed(True, "🎉 Tất cả các bước đã hoàn thành!")
            else:
                manager.set_completed(False, "⚠️ Đã bị hủy bởi người dùng")
                
        except Exception as e:
            manager.set_completed(False, f"❌ Lỗi: {str(e)}")
    
    # Create and show dialog
    dialog = SimpleProgressDialog("Test Simple Progress")
    dialog.show_and_run(simulate_work)

if __name__ == "__main__":
    test_simple_progress()
