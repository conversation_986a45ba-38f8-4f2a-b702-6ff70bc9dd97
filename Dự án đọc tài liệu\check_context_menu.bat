@echo off
echo === KIỂM TRA CONTEXT MENU ===
echo.

echo 1. Kiểm tra Registry:
reg query "HKEY_CLASSES_ROOT\*\shell\RenamePDF" 2>nul
if %errorlevel% == 0 (
    echo    ✓ Context menu đã được đăng ký
    echo.
    echo 2. Kiểm tra command:
    reg query "HKEY_CLASSES_ROOT\*\shell\RenamePDF\command" 2>nul
) else (
    echo    ✗ Context menu chưa được đăng ký
)

echo.
echo 3. Kiểm tra file cấu hình:
if exist "context_menu_config.json" (
    echo    ✓ File context_menu_config.json tồn tại
    type "context_menu_config.json"
) else (
    echo    ✗ File context_menu_config.json không tồn tại
)

echo.
echo 4. Kiểm tra file rename_pdf.py:
if exist "rename_pdf.py" (
    echo    ✓ File rename_pdf.py tồn tại
) else (
    echo    ✗ File rename_pdf.py không tồn tại
)

echo.
echo 5. Ki<PERSON>m tra Python:
python --version 2>nul
if %errorlevel% == 0 (
    echo    ✓ Python có thể chạy được
) else (
    echo    ✗ Python không thể chạy được
)

echo.
pause
