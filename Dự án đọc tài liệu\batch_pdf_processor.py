#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Batch PDF Processor - Xử lý nhiều file PDF cùng lúc với giao diện người dùng
"""

import sys
import os
import time
import threading
from datetime import datetime
from pathlib import Path

# GUI imports
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QListWidget,
                            QListWidgetItem, QProgressBar, QTextEdit,
                            QFileDialog, QMessageBox, QGroupBox, QSplitter,
                            QCheckBox, QSpinBox, QComboBox, QTabWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QTextCursor

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import our modules
try:
    from rename_pdf import process_pdf_with_progress, setup_logging
    from config_manager import ConfigManager
    from simple_progress import SimpleProgressManager
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Some modules not available: {e}")
    MODULES_AVAILABLE = False

class BatchProgressManager:
    """Progress manager cho batch processing"""
    
    def __init__(self, total_files, update_callback=None):
        self.total_files = total_files
        self.current_file = 0
        self.update_callback = update_callback
        self.cancelled = False
        
    def start_file(self, file_index, filename):
        """Bắt đầu xử lý file mới"""
        self.current_file = file_index
        if self.update_callback:
            self.update_callback(f"📄 File {file_index + 1}/{self.total_files}: {filename}")
    
    def update_file_progress(self, progress, detail=""):
        """Cập nhật tiến trình file hiện tại"""
        overall_progress = ((self.current_file * 100) + progress) / self.total_files
        if self.update_callback:
            self.update_callback(f"📊 {overall_progress:.1f}% - {detail}")
    
    def complete_file(self, success, message=""):
        """Hoàn thành file hiện tại"""
        if self.update_callback:
            status = "✅" if success else "❌"
            self.update_callback(f"{status} File {self.current_file + 1}: {message}")
    
    def set_cancelled(self):
        """Đánh dấu bị hủy"""
        self.cancelled = True
    
    def is_cancelled(self):
        """Kiểm tra có bị hủy không"""
        return self.cancelled

class PDFProcessorThread(QThread):
    """Thread xử lý PDF để không block UI"""
    
    # Signals
    progress_updated = pyqtSignal(str)  # Progress message
    file_completed = pyqtSignal(int, bool, str)  # file_index, success, message
    batch_completed = pyqtSignal(bool, str)  # success, summary
    
    def __init__(self, pdf_files, config):
        super().__init__()
        self.pdf_files = pdf_files
        self.config = config
        self.cancelled = False
        
    def run(self):
        """Chạy batch processing"""
        try:
            # Kiểm tra chế độ xử lý từ parent window
            use_batch_parallel = getattr(self, 'use_batch_parallel', True)

            if use_batch_parallel:
                self.run_batch_parallel()
            else:
                self.run_sequential()

        except Exception as e:
            self.batch_completed.emit(False, f"❌ Lỗi nghiêm trọng: {str(e)}")

    def run_batch_parallel(self):
        """Chạy batch parallel processing"""
        try:
            from batch_parallel_processor import BatchParallelProcessor

            # Tạo batch processor
            batch_processor = BatchParallelProcessor(
                self.pdf_files,
                self.config,
                self.progress_updated.emit
            )

            # Chạy batch processing
            success = batch_processor.process_all_files()

            # Emit file completion signals
            for i, pdf_path in enumerate(self.pdf_files):
                result = batch_processor.results.get(pdf_path, {'success': False, 'message': 'Unknown'})
                self.file_completed.emit(i, result['success'], result['message'])

            # Tạo summary report
            summary = batch_processor.get_summary_report()
            self.batch_completed.emit(success, summary)

        except ImportError:
            self.progress_updated.emit("⚠️ Batch parallel processor không khả dụng, chuyển sang sequential...")
            self.run_sequential()
        except Exception as e:
            self.batch_completed.emit(False, f"❌ Lỗi batch parallel: {str(e)}")

    def run_sequential(self):
        """Chạy sequential processing (original logic)"""
        try:
            total_files = len(self.pdf_files)
            success_count = 0
            failed_files = []

            self.progress_updated.emit(f"🚀 Bắt đầu xử lý tuần tự {total_files} file PDF...")

            for i, pdf_path in enumerate(self.pdf_files):
                if self.cancelled:
                    break

                filename = os.path.basename(pdf_path)
                self.progress_updated.emit(f"📄 File {i + 1}/{total_files}: {filename}")

                try:
                    # Tạo progress manager cho file này
                    file_progress = FileProgressManager(self.progress_updated.emit)

                    # Set steps như trong rename_pdf.py
                    steps = [
                        ("Kiểm tra file và cấu hình", 10),
                        ("Chuyển đổi PDF thành ảnh", 20),
                        ("OCR văn bản", 30),
                        ("Trích xuất thông tin với AI", 25),
                        ("Lưu kết quả", 15)
                    ]
                    file_progress.set_steps(steps)

                    # Xử lý file
                    success = self.process_single_file(pdf_path, file_progress)

                    if success:
                        success_count += 1
                        self.file_completed.emit(i, True, f"✅ Hoàn thành: {filename}")
                    else:
                        failed_files.append(filename)
                        self.file_completed.emit(i, False, f"❌ Thất bại: {filename}")

                except Exception as e:
                    failed_files.append(filename)
                    self.file_completed.emit(i, False, f"❌ Lỗi: {str(e)}")

            # Tóm tắt kết quả
            if not self.cancelled:
                summary = f"🎉 Hoàn thành sequential processing!\n"
                summary += f"✅ Thành công: {success_count}/{total_files} file\n"
                if failed_files:
                    summary += f"❌ Thất bại: {len(failed_files)} file\n"
                    summary += f"Files lỗi: {', '.join(failed_files[:3])}"
                    if len(failed_files) > 3:
                        summary += f" và {len(failed_files) - 3} file khác"

                self.batch_completed.emit(success_count > 0, summary)
            else:
                self.batch_completed.emit(False, "⚠️ Sequential processing đã bị hủy")

        except Exception as e:
            self.batch_completed.emit(False, f"❌ Lỗi sequential processing: {str(e)}")
    
    def process_single_file(self, pdf_path, progress_manager):
        """Xử lý một file PDF"""
        try:
            if not MODULES_AVAILABLE:
                raise Exception("Modules không khả dụng")
                
            # Gọi function xử lý từ rename_pdf.py
            process_pdf_with_progress(pdf_path, progress_manager)
            return True
            
        except Exception as e:
            progress_manager.set_completed(False, f"Lỗi: {str(e)}")
            return False
    
    def cancel(self):
        """Hủy processing"""
        self.cancelled = True

class FileProgressManager:
    """Progress manager cho từng file - Compatible với rename_pdf.py"""

    def __init__(self, update_callback):
        self.update_callback = update_callback
        self.cancelled = False
        self.steps = []
        self.current_step = 0
        self.total_steps = 0
        self.step_weights = []
        self.total_weight = 100

    def set_steps(self, steps):
        """Set danh sách các bước với trọng số"""
        self.steps = steps
        self.total_steps = len(steps)
        self.step_weights = [weight for _, weight in steps]
        self.total_weight = sum(self.step_weights)
        self.current_step = 0

    def start_step(self, step_index, detail=""):
        """Bắt đầu một bước"""
        self.current_step = step_index
        if step_index < len(self.steps):
            step_name = self.steps[step_index][0]
            self.update_callback(f"   🔄 Bước {step_index + 1}: {step_name}")
            if detail:
                self.update_callback(f"      {detail}")

    def update_step_progress(self, progress, detail=""):
        """Update progress trong bước hiện tại"""
        if self.current_step < len(self.steps):
            step_name = self.steps[self.current_step][0]
            self.update_callback(f"   📊 {step_name}: {progress:.1f}%")
            if detail:
                self.update_callback(f"      {detail}")

    def complete_step(self, detail=""):
        """Hoàn thành bước hiện tại"""
        if self.current_step < len(self.steps):
            step_name = self.steps[self.current_step][0]
            self.update_callback(f"   ✅ Hoàn thành: {step_name}")
            if detail:
                self.update_callback(f"      {detail}")

    def set_completed(self, success, message=""):
        """Hoàn thành toàn bộ quá trình"""
        status = "✅" if success else "❌"
        self.update_callback(f"   {status} {message}")

    def is_cancelled(self):
        """Kiểm tra xem có bị hủy không"""
        return self.cancelled

class BatchPDFProcessor(QMainWindow):
    """Main window cho batch PDF processor"""
    
    def __init__(self):
        super().__init__()
        self.pdf_files = []
        self.config = None
        self.processor_thread = None

        self.init_ui()
        self.load_config()

    def safe_status_message(self, message):
        """Safely update status bar message"""
        try:
            if hasattr(self, 'statusBar') and self.statusBar():
                self.statusBar().showMessage(message)
        except Exception:
            pass  # Ignore status bar errors
        
    def init_ui(self):
        """Khởi tạo giao diện"""
        self.setWindowTitle("📄 Batch PDF Processor - Xử lý nhiều PDF cùng lúc")
        self.setGeometry(100, 100, 1000, 700)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        
        # Title
        title_label = QLabel("📄 Batch PDF Processor")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # Splitter for main content
        splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(splitter)
        
        # Left panel - File list
        self.setup_file_panel(splitter)
        
        # Right panel - Progress and logs
        self.setup_progress_panel(splitter)
        
        # Bottom controls
        self.setup_controls(layout)
        
        # Status bar
        self.safe_status_message("Sẵn sàng xử lý PDF")
        
    def setup_file_panel(self, parent):
        """Setup panel danh sách file"""
        file_widget = QWidget()
        file_layout = QVBoxLayout(file_widget)
        
        # File list group
        file_group = QGroupBox("📁 Danh sách file PDF")
        file_group_layout = QVBoxLayout(file_group)
        
        # File list
        self.file_list = QListWidget()
        self.file_list.setMinimumHeight(300)
        self.file_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        file_group_layout.addWidget(self.file_list)
        
        # File buttons
        file_buttons = QHBoxLayout()
        
        self.add_files_btn = QPushButton("➕ Thêm file")
        self.add_files_btn.clicked.connect(self.add_files)
        file_buttons.addWidget(self.add_files_btn)
        
        self.add_folder_btn = QPushButton("📁 Thêm thư mục")
        self.add_folder_btn.clicked.connect(self.add_folder)
        file_buttons.addWidget(self.add_folder_btn)
        
        self.remove_selected_btn = QPushButton("➖ Xóa đã chọn")
        self.remove_selected_btn.clicked.connect(self.remove_selected_files)
        file_buttons.addWidget(self.remove_selected_btn)

        self.clear_btn = QPushButton("🗑️ Xóa tất cả")
        self.clear_btn.clicked.connect(self.clear_files)
        file_buttons.addWidget(self.clear_btn)
        
        file_group_layout.addLayout(file_buttons)
        file_layout.addWidget(file_group)
        
        # Settings group
        settings_group = QGroupBox("⚙️ Cài đặt")
        settings_layout = QVBoxLayout(settings_group)

        # Continue on error
        continue_layout = QHBoxLayout()
        continue_layout.addWidget(QLabel("Tiếp tục khi lỗi:"))
        self.continue_on_error = QCheckBox("Bật")
        self.continue_on_error.setChecked(True)
        self.continue_on_error.setToolTip("Tiếp tục xử lý file khác khi một file bị lỗi")
        continue_layout.addWidget(self.continue_on_error)
        continue_layout.addStretch()
        settings_layout.addLayout(continue_layout)

        # Auto scroll
        scroll_layout = QHBoxLayout()
        scroll_layout.addWidget(QLabel("Tự động cuộn:"))
        self.auto_scroll = QCheckBox("Bật")
        self.auto_scroll.setChecked(True)
        self.auto_scroll.setToolTip("Tự động cuộn xuống khi có log mới")
        scroll_layout.addWidget(self.auto_scroll)
        scroll_layout.addStretch()
        settings_layout.addLayout(scroll_layout)

        # Show details
        details_layout = QHBoxLayout()
        details_layout.addWidget(QLabel("Hiển thị chi tiết:"))
        self.show_details = QCheckBox("Bật")
        self.show_details.setChecked(True)
        self.show_details.setToolTip("Hiển thị chi tiết từng bước xử lý")
        details_layout.addWidget(self.show_details)
        details_layout.addStretch()
        settings_layout.addLayout(details_layout)

        # Processing mode
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("Chế độ xử lý:"))
        self.processing_mode = QComboBox()
        self.processing_mode.addItems([
            "Sequential (Tuần tự từng file)",
            "Batch Parallel (Đồng loạt theo bước)"
        ])
        self.processing_mode.setCurrentIndex(1)  # Default to batch parallel
        self.processing_mode.setToolTip("Sequential: Xử lý từng file một\nBatch Parallel: Xử lý tất cả file cùng bước")
        mode_layout.addWidget(self.processing_mode)
        mode_layout.addStretch()
        settings_layout.addLayout(mode_layout)
        
        file_layout.addWidget(settings_group)
        file_layout.addStretch()
        
        parent.addWidget(file_widget)
        
    def setup_progress_panel(self, parent):
        """Setup panel tiến trình và log"""
        progress_widget = QWidget()
        progress_layout = QVBoxLayout(progress_widget)
        
        # Progress group
        progress_group = QGroupBox("📊 Tiến trình xử lý")
        progress_group_layout = QVBoxLayout(progress_group)
        
        # Overall progress
        self.overall_progress = QProgressBar()
        self.overall_progress.setVisible(False)
        progress_group_layout.addWidget(self.overall_progress)
        
        # Progress text
        self.progress_text = QTextEdit()
        self.progress_text.setMaximumHeight(200)
        self.progress_text.setReadOnly(True)
        progress_group_layout.addWidget(self.progress_text)
        
        progress_layout.addWidget(progress_group)
        
        # Results group
        results_group = QGroupBox("📋 Kết quả")
        results_layout = QVBoxLayout(results_group)
        
        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        results_layout.addWidget(self.results_text)
        
        progress_layout.addWidget(results_group)
        
        parent.addWidget(progress_widget)
        
    def setup_controls(self, parent):
        """Setup controls"""
        controls_layout = QHBoxLayout()
        
        # Info label
        self.info_label = QLabel("Chọn file PDF để bắt đầu")
        controls_layout.addWidget(self.info_label)
        
        controls_layout.addStretch()
        
        # Control buttons
        self.start_btn = QPushButton("🚀 Bắt đầu xử lý")
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setEnabled(False)
        controls_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ Dừng")
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setEnabled(False)
        controls_layout.addWidget(self.stop_btn)
        
        self.config_btn = QPushButton("⚙️ Cấu hình")
        self.config_btn.clicked.connect(self.open_config)
        controls_layout.addWidget(self.config_btn)
        
        parent.addLayout(controls_layout)
        
    def load_config(self):
        """Load configuration"""
        try:
            if MODULES_AVAILABLE:
                self.config = ConfigManager()
                self.safe_status_message("✅ Đã tải cấu hình")
            else:
                self.safe_status_message("⚠️ Modules không khả dụng")
        except Exception as e:
            self.safe_status_message(f"❌ Lỗi tải cấu hình: {e}")
            
    def add_files(self):
        """Thêm file PDF"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "Chọn file PDF", "", "PDF Files (*.pdf)"
        )
        
        for file_path in files:
            if file_path not in self.pdf_files:
                self.pdf_files.append(file_path)
                item = QListWidgetItem(f"📄 {os.path.basename(file_path)}")
                item.setToolTip(file_path)
                self.file_list.addItem(item)
        
        self.update_ui_state()
        
    def add_folder(self):
        """Thêm tất cả PDF trong thư mục"""
        folder = QFileDialog.getExistingDirectory(self, "Chọn thư mục chứa PDF")
        
        if folder:
            pdf_files = list(Path(folder).glob("*.pdf"))
            added_count = 0
            
            for pdf_file in pdf_files:
                file_path = str(pdf_file)
                if file_path not in self.pdf_files:
                    self.pdf_files.append(file_path)
                    item = QListWidgetItem(f"📄 {pdf_file.name}")
                    item.setToolTip(file_path)
                    self.file_list.addItem(item)
                    added_count += 1
            
            if added_count > 0:
                self.safe_status_message(f"✅ Đã thêm {added_count} file PDF")
            else:
                self.safe_status_message("ℹ️ Không có file PDF mới")
        
        self.update_ui_state()
        
    def remove_selected_files(self):
        """Xóa file được chọn"""
        selected_items = self.file_list.selectedItems()
        if not selected_items:
            return

        # Get selected file paths
        selected_paths = []
        for item in selected_items:
            file_path = item.toolTip()
            selected_paths.append(file_path)

        # Remove from list
        for file_path in selected_paths:
            if file_path in self.pdf_files:
                self.pdf_files.remove(file_path)

        # Remove from UI
        for item in selected_items:
            row = self.file_list.row(item)
            self.file_list.takeItem(row)

        self.update_ui_state()
        self.safe_status_message(f"➖ Đã xóa {len(selected_paths)} file")

    def clear_files(self):
        """Xóa tất cả file"""
        self.pdf_files.clear()
        self.file_list.clear()
        self.update_ui_state()
        self.safe_status_message("🗑️ Đã xóa tất cả file")
        
    def update_ui_state(self):
        """Cập nhật trạng thái UI"""
        has_files = len(self.pdf_files) > 0
        is_processing = bool(self.processor_thread and self.processor_thread.isRunning())

        self.start_btn.setEnabled(has_files and not is_processing and MODULES_AVAILABLE)
        self.stop_btn.setEnabled(is_processing)
        self.add_files_btn.setEnabled(not is_processing)
        self.add_folder_btn.setEnabled(not is_processing)
        self.clear_btn.setEnabled(has_files and not is_processing)
        self.remove_selected_btn.setEnabled(has_files and not is_processing)
        
        if has_files:
            self.info_label.setText(f"📄 {len(self.pdf_files)} file PDF đã chọn")
        else:
            self.info_label.setText("Chọn file PDF để bắt đầu")
            
    def start_processing(self):
        """Bắt đầu xử lý batch"""
        if not self.pdf_files:
            return
            
        if not MODULES_AVAILABLE:
            QMessageBox.warning(self, "Lỗi", "Modules không khả dụng")
            return
            
        # Clear previous results
        self.progress_text.clear()
        self.results_text.clear()
        
        # Start processing thread
        self.processor_thread = PDFProcessorThread(self.pdf_files, self.config)

        # Set processing mode
        use_batch_parallel = self.processing_mode.currentIndex() == 1  # 1 = Batch Parallel
        self.processor_thread.use_batch_parallel = use_batch_parallel

        self.processor_thread.progress_updated.connect(self.update_progress)
        self.processor_thread.file_completed.connect(self.file_completed)
        self.processor_thread.batch_completed.connect(self.batch_completed)
        self.processor_thread.start()

        # Log processing mode
        mode_name = "Batch Parallel" if use_batch_parallel else "Sequential"
        self.update_progress(f"🔧 Chế độ xử lý: {mode_name}")
        
        self.update_ui_state()
        self.safe_status_message("🚀 Đang xử lý batch...")

    def stop_processing(self):
        """Dừng xử lý"""
        if self.processor_thread:
            self.processor_thread.cancel()
            self.safe_status_message("⏹️ Đang dừng...")
            
    def update_progress(self, message):
        """Cập nhật tiến trình"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Filter detailed messages if not enabled
        if not self.show_details.isChecked() and message.strip().startswith("   "):
            return

        self.progress_text.append(f"[{timestamp}] {message}")

        # Auto scroll to bottom if enabled
        if self.auto_scroll.isChecked():
            cursor = self.progress_text.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.End)
            self.progress_text.setTextCursor(cursor)
        
    def file_completed(self, file_index, success, message):
        """File đã hoàn thành"""
        # Update file status in list if needed
        if 0 <= file_index < self.file_list.count():
            item = self.file_list.item(file_index)
            if item:
                status_icon = "✅" if success else "❌"
                original_text = item.text()
                if not original_text.startswith(("✅", "❌")):
                    item.setText(f"{status_icon} {original_text}")

        self.results_text.append(message)
        
    def batch_completed(self, success, summary):
        """Batch đã hoàn thành"""
        self.results_text.append(f"\n{summary}")
        self.update_ui_state()

        if success:
            self.safe_status_message("✅ Batch processing hoàn thành")
        else:
            self.safe_status_message("❌ Batch processing thất bại")
            
        # Show notification
        QMessageBox.information(self, "Hoàn thành", summary)
        
    def open_config(self):
        """Mở cấu hình"""
        try:
            if MODULES_AVAILABLE:
                from config_manager import ConfigWindow
                self.config_window = ConfigWindow()
                self.config_window.show()
            else:
                QMessageBox.warning(self, "Lỗi", "Config manager không khả dụng")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể mở cấu hình: {e}")

def main():
    """Main function"""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Batch PDF Processor")
    app.setApplicationVersion("1.0")
    
    # Create and show main window
    window = BatchPDFProcessor()
    window.show()
    
    # Run application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
