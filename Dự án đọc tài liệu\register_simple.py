#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Đăng ký context menu cho phiên bản simple
"""

import os
import sys
import winreg

def register_simple():
    """Đăng ký context menu simple"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Tìm Python
        python_paths = [
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
            r"C:\Python313\pythonw.exe",
            "pythonw.exe"
        ]
        
        python_path = "pythonw.exe"
        for path in python_paths:
            if os.path.exists(path):
                python_path = path
                break
        
        script_path = os.path.join(script_dir, "rename_pdf_simple.py")
        
        print(f"Python: {python_path}")
        print(f"Script: {script_path}")
        
        # Xóa key cũ
        try:
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDFSimple\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDFSimple")
        except:
            pass
        
        # Tạo key mới
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDFSimple") as key:
            winreg.SetValue(key, "", winreg.REG_SZ, "Đổi tên PDF (Simple)")
        
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDFSimple\command") as key:
            command = f'"{python_path}" "{script_path}" "%1"'
            winreg.SetValue(key, "", winreg.REG_SZ, command)
            print(f"Command: {command}")
        
        print("✓ Đăng ký Simple thành công!")
        print("Hãy thử nhấn chuột phải vào file PDF và chọn 'Đổi tên PDF (Simple)'")
        return True
        
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def main():
    print("=== ĐĂNG KÝ SIMPLE CONTEXT MENU ===")
    register_simple()

if __name__ == "__main__":
    main()
