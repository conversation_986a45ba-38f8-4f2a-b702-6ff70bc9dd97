# 📄 Batch PDF Processor - Hướng dẫn sử dụng

## 🎯 Tổng quan

**Batch PDF Processor** là công cụ xử lý nhiều file PDF cùng lúc với giao diện người dùng thân thiện. Thay vì xử lý từng file một, bạn có thể chọn nhiều file và để chương trình tự động xử lý tất cả.

## ✨ Tính năng chính

### 📁 **File Management**
- ✅ **Chọn nhiều file**: Thêm nhiều PDF cùng lúc
- ✅ **Thêm thư mục**: Tự động tìm tất cả PDF trong folder
- ✅ **Xóa selective**: Xóa file được chọn
- ✅ **Drag & Drop**: Kéo thả file vào danh sách (planned)

### 🚀 **Batch Processing**
- ✅ **Xử lý tuần tự**: Xử lý từng file một cách an toàn
- ✅ **Progress tracking**: <PERSON> dõi tiến trình real-time
- ✅ **Error handling**: Tiếp tục khi có file lỗi
- ✅ **Detailed logging**: Log chi tiết từng bước

### 🎛️ **User Interface**
- ✅ **Split layout**: File list + Progress panel
- ✅ **Real-time updates**: Cập nhật trạng thái ngay lập tức
- ✅ **Configurable**: Tùy chỉnh hiển thị
- ✅ **Professional**: Giao diện chuyên nghiệp

## 🚀 Cách chạy

### 1. **Chạy trực tiếp**
```bash
python batch_pdf_processor.py
```

### 2. **Chạy từ launcher** (nếu có)
```bash
python project_launcher.py
# Chọn "Batch PDF Processor"
```

## 📖 Hướng dẫn sử dụng

### Bước 1: **Thêm file PDF**

#### 🔹 **Thêm file lẻ**
1. Nhấn nút **"➕ Thêm file"**
2. Chọn một hoặc nhiều file PDF
3. File sẽ xuất hiện trong danh sách

#### 🔹 **Thêm cả thư mục**
1. Nhấn nút **"📁 Thêm thư mục"**
2. Chọn thư mục chứa PDF
3. Tất cả PDF trong thư mục sẽ được thêm

#### 🔹 **Quản lý danh sách**
- **Xóa file được chọn**: Chọn file → Nhấn **"➖ Xóa đã chọn"**
- **Xóa tất cả**: Nhấn **"🗑️ Xóa tất cả"**
- **Xem đường dẫn**: Hover chuột lên file

### Bước 2: **Cấu hình settings**

#### ⚙️ **Settings Panel**
- **📄 Tiếp tục khi lỗi**: Không dừng khi 1 file bị lỗi
- **📜 Tự động cuộn**: Auto scroll log xuống dưới
- **🔍 Hiển thị chi tiết**: Show detailed progress steps

### Bước 3: **Bắt đầu xử lý**

1. **Kiểm tra cấu hình**: Nhấn **"⚙️ Cấu hình"** nếu cần
2. **Bắt đầu**: Nhấn **"🚀 Bắt đầu xử lý"**
3. **Theo dõi**: Xem progress trong panel bên phải
4. **Dừng nếu cần**: Nhấn **"⏹️ Dừng"**

### Bước 4: **Xem kết quả**

- **📊 Progress Panel**: Tiến trình real-time
- **📋 Results Panel**: Kết quả từng file
- **💬 Notification**: Thông báo khi hoàn thành

## 🎨 Giao diện

### 📱 **Layout**
```
┌─────────────────────────────────────────────────────┐
│                📄 Batch PDF Processor               │
├─────────────────┬───────────────────────────────────┤
│  📁 File List   │     📊 Progress & Results        │
│                 │                                   │
│ ➕ Thêm file     │  [Progress Bar]                   │
│ 📁 Thêm folder  │                                   │
│ ➖ Xóa đã chọn   │  📊 Progress Log:                 │
│ 🗑️ Xóa tất cả   │  [10:30:15] 📄 File 1/5: doc.pdf │
│                 │  [10:30:16] 🔄 Bước 1: Kiểm tra   │
│ ⚙️ Settings:    │  [10:30:17] ✅ Hoàn thành         │
│ ☑️ Tiếp tục lỗi  │                                   │
│ ☑️ Auto scroll  │  📋 Results:                      │
│ ☑️ Chi tiết     │  ✅ File 1: Thành công            │
│                 │  ❌ File 2: Lỗi OCR               │
├─────────────────┴───────────────────────────────────┤
│ 📄 5 files │🚀 Bắt đầu │⏹️ Dừng │⚙️ Cấu hình │
└─────────────────────────────────────────────────────┘
```

### 🎯 **UI Components**

#### 📁 **File List Panel**
- **File list**: Danh sách PDF với multi-select
- **Add buttons**: Thêm file/folder
- **Remove buttons**: Xóa selective/all
- **Settings**: Cấu hình xử lý

#### 📊 **Progress Panel**
- **Progress bar**: Thanh tiến trình tổng thể
- **Progress log**: Log real-time từng bước
- **Results log**: Kết quả từng file

#### 🎛️ **Control Panel**
- **Info label**: Hiển thị số file đã chọn
- **Start button**: Bắt đầu xử lý
- **Stop button**: Dừng xử lý
- **Config button**: Mở cấu hình

## ⚡ Performance

### 📊 **Processing Speed**
- **Sequential**: Xử lý tuần tự để đảm bảo ổn định
- **Memory efficient**: Giải phóng memory sau mỗi file
- **Error resilient**: Tiếp tục khi có lỗi

### 🔧 **Resource Usage**
- **CPU**: Moderate usage per file
- **Memory**: ~200-500MB per PDF
- **Disk**: Temporary files cleaned up
- **Network**: API calls for OCR/AI

## 🐛 Troubleshooting

### ❌ **Common Issues**

#### 1. **"Modules không khả dụng"**
```bash
# Kiểm tra dependencies
python -c "import PyQt5; print('PyQt5 OK')"
python -c "from rename_pdf import process_pdf_with_progress; print('rename_pdf OK')"
python -c "from config_manager import ConfigManager; print('config_manager OK')"
```

#### 2. **"Không thể mở cấu hình"**
- Đảm bảo `config_manager.py` tồn tại
- Chạy `python config_manager.py` để test

#### 3. **"Xử lý file thất bại"**
- Kiểm tra file PDF không bị corrupt
- Kiểm tra API keys trong config
- Xem log chi tiết trong Progress panel

#### 4. **"GUI không hiển thị"**
- Cài đặt PyQt5: `pip install PyQt5`
- Kiểm tra display settings
- Chạy test: `python test_batch_processor.py`

### 🔍 **Debug Mode**
```bash
# Chạy với debug
python batch_pdf_processor.py --debug

# Xem log chi tiết
python view_debug_log.py
```

## 📋 Requirements

### 🐍 **Python Packages**
```
PyQt5>=5.15.0
pdf2image>=3.1.0
google-cloud-vision>=3.4.0
google-generativeai>=0.3.0
openai>=1.0.0
groq>=0.4.0
gspread>=5.10.0
Pillow>=9.5.0
```

### 🔧 **System Requirements**
- **Python**: 3.8+
- **OS**: Windows/Linux/macOS
- **RAM**: 4GB+ recommended
- **Disk**: 1GB+ free space

### 🌐 **API Requirements**
- **Google Cloud Vision**: OCR
- **Google Gemini**: AI processing
- **OpenAI** (optional): Alternative AI
- **Grok** (optional): Alternative AI
- **Google Sheets**: Results storage

## 🎉 Benefits

### 🚀 **Efficiency**
- **Batch processing**: Xử lý nhiều file cùng lúc
- **Unattended operation**: Chạy tự động
- **Error recovery**: Tiếp tục khi có lỗi
- **Progress tracking**: Biết được tiến trình

### 👤 **User Experience**
- **Visual interface**: Giao diện trực quan
- **Real-time feedback**: Phản hồi ngay lập tức
- **Flexible configuration**: Tùy chỉnh linh hoạt
- **Professional appearance**: Giao diện chuyên nghiệp

### 🔧 **Maintainability**
- **Modular design**: Thiết kế module
- **Error handling**: Xử lý lỗi tốt
- **Logging**: Log chi tiết
- **Extensible**: Dễ mở rộng

## 🔮 Future Features

### 🎯 **Planned**
- **Parallel processing**: Xử lý song song
- **Drag & Drop**: Kéo thả file
- **Progress persistence**: Lưu tiến trình
- **Batch templates**: Template xử lý
- **Export results**: Xuất kết quả

### 💡 **Ideas**
- **Cloud integration**: Tích hợp cloud
- **Scheduling**: Lên lịch xử lý
- **Notifications**: Thông báo email/SMS
- **Analytics**: Thống kê xử lý

---

**🎊 Batch PDF Processor giúp bạn xử lý hàng loạt PDF một cách hiệu quả và chuyên nghiệp!**
