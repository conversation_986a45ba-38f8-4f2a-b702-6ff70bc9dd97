#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script đơn giản để sửa context menu
"""

import os
import sys
import winreg

def register_context_menu():
    """Đăng ký context menu trực tiếp"""
    try:
        # Đ<PERSON>ờng dẫn script hiện tại
        script_dir = os.path.dirname(os.path.abspath(__file__))
        rename_script = os.path.join(script_dir, 'rename_pdf.py')
        
        # Đường dẫn Python (thử nhiều vị trí)
        python_paths = [
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
            r"C:\Python313\pythonw.exe",
            "pythonw.exe"
        ]
        
        python_path = None
        for path in python_paths:
            if os.path.exists(path):
                python_path = path
                break
        
        if not python_path:
            python_path = "pythonw.exe"  # Fallback
        
        print(f"Using Python: {python_path}")
        print(f"Using script: {rename_script}")
        
        # Tạo key cho PDF files
        pdf_key_path = r"*\shell\RenamePDF"
        
        # Tạo key chính
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path) as key:
            winreg.SetValue(key, "", winreg.REG_SZ, "Đổi tên PDF")
        
        # Tạo command key
        command_key_path = pdf_key_path + r"\command"
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, command_key_path) as key:
            command = f'"{python_path}" "{rename_script}" "%1"'
            winreg.SetValue(key, "", winreg.REG_SZ, command)
            print(f"Registered command: {command}")
        
        print("✓ Context menu đã được đăng ký thành công!")
        return True
        
    except PermissionError:
        print("✗ Cần quyền Administrator để đăng ký context menu")
        print("Hãy chạy script này với quyền Administrator")
        return False
    except Exception as e:
        print(f"✗ Lỗi khi đăng ký context menu: {e}")
        return False

def check_context_menu():
    """Kiểm tra context menu"""
    try:
        with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF"):
            print("✓ Context menu đã được đăng ký")
            
        with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF\command") as key:
            command = winreg.QueryValue(key, "")
            print(f"Command: {command}")
            
        return True
    except FileNotFoundError:
        print("✗ Context menu chưa được đăng ký")
        return False
    except Exception as e:
        print(f"✗ Lỗi khi kiểm tra context menu: {e}")
        return False

def main():
    print("=== FIX CONTEXT MENU ===")
    
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        check_context_menu()
    else:
        print("Đang đăng ký context menu...")
        if register_context_menu():
            print("\nKiểm tra lại:")
            check_context_menu()

if __name__ == "__main__":
    main()
