@echo off
chcp 65001 >nul
title Batch PDF Processor

echo.
echo ========================================
echo Batch PDF Processor
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found!
    echo Please install Python and add to PATH
    pause
    exit /b 1
)

REM Check if batch_pdf_processor.py exists
if not exist "batch_pdf_processor.py" (
    echo ERROR: batch_pdf_processor.py not found!
    echo Please ensure this file is in the same directory
    pause
    exit /b 1
)

echo Starting Batch PDF Processor...
echo.

REM Run the batch processor
python batch_pdf_processor.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo ERROR: Batch PDF Processor encountered an error
    echo.
    echo Troubleshooting suggestions:
    echo    1. Check dependencies: pip install -r requirements.txt
    echo    2. Run test: python test_batch_processor.py
    echo    3. View log: python view_debug_log.py
    echo.
) else (
    echo.
    echo SUCCESS: Batch PDF Processor closed successfully
    echo.
)

pause
