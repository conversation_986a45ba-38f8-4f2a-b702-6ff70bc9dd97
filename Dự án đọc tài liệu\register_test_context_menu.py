#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Đăng ký context menu test
"""

import os
import sys
import winreg

def register_test_context_menu():
    """Đăng ký context menu test"""
    try:
        # Đường dẫn script hiệ<PERSON> tại
        script_dir = os.path.dirname(os.path.abspath(__file__))
        test_script = os.path.join(script_dir, 'test_rename_simple.py')
        
        # Đường dẫn Python
        python_paths = [
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\pythonw.exe",
            r"C:\Python313\pythonw.exe",
            "pythonw.exe"
        ]
        
        python_path = None
        for path in python_paths:
            if os.path.exists(path):
                python_path = path
                break
        
        if not python_path:
            python_path = "pythonw.exe"
        
        print(f"Using Python: {python_path}")
        print(f"Using script: {test_script}")
        
        # Xóa key cũ nếu có
        try:
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\TestRenamePDF\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\TestRenamePDF")
        except:
            pass
        
        # Tạo key mới
        pdf_key_path = r"*\shell\TestRenamePDF"
        
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path) as key:
            winreg.SetValue(key, "", winreg.REG_SZ, "Test Đổi tên PDF")
        
        command_key_path = pdf_key_path + r"\command"
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, command_key_path) as key:
            command = f'"{python_path}" "{test_script}" "%1"'
            winreg.SetValue(key, "", winreg.REG_SZ, command)
            print(f"Registered command: {command}")
        
        print("✓ Test context menu đã được đăng ký thành công!")
        print("Hãy thử nhấn chuột phải vào file PDF và chọn 'Test Đổi tên PDF'")
        return True
        
    except PermissionError:
        print("✗ Cần quyền Administrator")
        return False
    except Exception as e:
        print(f"✗ Lỗi: {e}")
        return False

def unregister_test_context_menu():
    """Hủy đăng ký test context menu"""
    try:
        winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\TestRenamePDF\command")
        winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\TestRenamePDF")
        print("✓ Test context menu đã được hủy đăng ký")
        return True
    except FileNotFoundError:
        print("Test context menu chưa được đăng ký")
        return False
    except Exception as e:
        print(f"✗ Lỗi khi hủy đăng ký: {e}")
        return False

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "unregister":
        unregister_test_context_menu()
    else:
        register_test_context_menu()

if __name__ == "__main__":
    main()
