#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phiên bản đơn giản của rename_pdf.py
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from datetime import datetime
import traceback
import json

def debug_log(message):
    """Ghi debug log"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        log_file = os.path.join(script_dir, 'rename_pdf_simple.log')
        
        with open(log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"{timestamp} - {message}\n")
    except:
        pass

def show_message(title, message):
    """Hiển thị thông báo"""
    try:
        root = tk.Tk()
        root.withdraw()
        if "Lỗi" in title:
            messagebox.showerror(title, message)
        else:
            messagebox.showinfo(title, message)
        root.destroy()
    except Exception as e:
        debug_log(f"Error showing message: {e}")

def load_config():
    """Load cấu hình"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        config_file = os.path.join(script_dir, 'config.json')
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                debug_log("Config loaded successfully")
                return config
        else:
            debug_log("Config file not found")
            return None
    except Exception as e:
        debug_log(f"Error loading config: {e}")
        return None

def check_dependencies():
    """Kiểm tra các thư viện cần thiết"""
    missing = []
    
    try:
        import google.generativeai as genai
        debug_log("✓ google.generativeai OK")
    except ImportError:
        missing.append("google-generativeai")
        debug_log("✗ google.generativeai missing")
    
    try:
        import openai
        debug_log("✓ openai OK")
    except ImportError:
        missing.append("openai")
        debug_log("✗ openai missing")
    
    try:
        from google.cloud import vision
        debug_log("✓ google.cloud.vision OK")
    except ImportError:
        missing.append("google-cloud-vision")
        debug_log("✗ google.cloud.vision missing")
    
    try:
        from pdf2image import convert_from_path
        debug_log("✓ pdf2image OK")
    except ImportError:
        missing.append("pdf2image")
        debug_log("✗ pdf2image missing")
    
    try:
        import gspread
        debug_log("✓ gspread OK")
    except ImportError:
        missing.append("gspread")
        debug_log("✗ gspread missing")
    
    return missing

def simple_rename_pdf(pdf_path):
    """Đổi tên PDF đơn giản"""
    try:
        debug_log(f"Starting simple rename for: {pdf_path}")
        
        # Kiểm tra config
        config = load_config()
        if not config:
            show_message("Lỗi", "Không tìm thấy file config.json.\nVui lòng chạy config_manager.py để tạo cấu hình.")
            return
        
        debug_log("Config OK")
        
        # Kiểm tra dependencies
        missing = check_dependencies()
        if missing:
            error_msg = f"Thiếu thư viện:\n{', '.join(missing)}\n\nVui lòng cài đặt: pip install {' '.join(missing)}"
            show_message("Lỗi", error_msg)
            return
        
        debug_log("Dependencies OK")
        
        # Hiển thị thông báo bắt đầu
        filename = os.path.basename(pdf_path)
        show_message("Bắt đầu xử lý", f"Đang xử lý file: {filename}\n\nQuá trình này có thể mất vài phút...")
        
        debug_log("Starting full processing...")
        
        # Import và chạy script chính
        try:
            # Import các module từ rename_pdf.py
            sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
            
            # Thử import process_pdf từ rename_pdf
            from rename_pdf import process_pdf
            debug_log("Successfully imported process_pdf")
            
            # Chạy xử lý
            process_pdf(pdf_path)
            debug_log("Processing completed successfully")
            
        except Exception as e:
            debug_log(f"Error in processing: {e}")
            debug_log(f"Traceback: {traceback.format_exc()}")
            show_message("Lỗi", f"Lỗi khi xử lý PDF:\n{str(e)}")
    
    except Exception as e:
        debug_log(f"Error in simple_rename_pdf: {e}")
        debug_log(f"Traceback: {traceback.format_exc()}")
        show_message("Lỗi", f"Lỗi không mong muốn:\n{str(e)}")

def main():
    debug_log("=== RENAME PDF SIMPLE STARTED ===")
    
    try:
        debug_log(f"Arguments: {sys.argv}")
        
        if len(sys.argv) > 1:
            pdf_path = sys.argv[1]
            debug_log(f"PDF path: {pdf_path}")
            
            # Kiểm tra file
            if not os.path.exists(pdf_path):
                error_msg = f"File không tồn tại: {os.path.basename(pdf_path)}"
                debug_log(f"ERROR: {error_msg}")
                show_message("Lỗi", error_msg)
                return
            
            if not pdf_path.lower().endswith('.pdf'):
                error_msg = f"File không phải PDF: {os.path.basename(pdf_path)}"
                debug_log(f"ERROR: {error_msg}")
                show_message("Lỗi", error_msg)
                return
            
            debug_log("File validation OK")
            
            # Xử lý PDF
            simple_rename_pdf(pdf_path)
            
        else:
            error_msg = "Không có file PDF được chọn"
            debug_log(f"ERROR: {error_msg}")
            show_message("Lỗi", error_msg)
    
    except Exception as e:
        debug_log(f"EXCEPTION in main: {e}")
        debug_log(f"Traceback: {traceback.format_exc()}")
        show_message("Lỗi", f"Lỗi nghiêm trọng:\n{str(e)}")
    
    debug_log("=== RENAME PDF SIMPLE ENDED ===")

if __name__ == "__main__":
    main()
