#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Đăng ký context menu chính cho rename_pdf.py
"""

import os
import sys
import winreg
import json

def load_config():
    """Load cấu hình từ file"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    config_file = os.path.join(script_dir, 'context_menu_config.json')
    
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Lỗi đọc config: {e}")
    
    # Default config
    return {
        "python_path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\pythonw.exe",
        "rename_script_path": os.path.join(script_dir, "rename_pdf.py"),
        "menu_text": "Đổi tên PDF",
        "icon_path": "",
        "use_vbs": False
    }

def register_main_context_menu():
    """Đăng ký context menu chính"""
    try:
        config = load_config()
        
        python_path = config["python_path"]
        script_path = config["rename_script_path"]
        menu_text = config["menu_text"]
        
        print(f"Python path: {python_path}")
        print(f"Script path: {script_path}")
        print(f"Menu text: {menu_text}")
        
        # Kiểm tra file tồn tại
        if not os.path.exists(python_path):
            print(f"CẢNH BÁO: Python không tồn tại tại {python_path}")
            # Thử tìm pythonw.exe khác
            alt_paths = [
                "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\pythonw.exe",
                "C:\\Python313\\pythonw.exe",
                "pythonw.exe"
            ]
            for alt_path in alt_paths:
                if os.path.exists(alt_path):
                    python_path = alt_path
                    print(f"Sử dụng Python thay thế: {python_path}")
                    break
        
        if not os.path.exists(script_path):
            print(f"LỖI: Script không tồn tại tại {script_path}")
            return False
        
        # Xóa key cũ nếu có
        try:
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF\command")
            winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF")
            print("Đã xóa context menu cũ")
        except:
            pass
        
        # Tạo key mới
        pdf_key_path = r"*\shell\RenamePDF"
        
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, pdf_key_path) as key:
            winreg.SetValue(key, "", winreg.REG_SZ, menu_text)
            
            # Thêm icon nếu có
            icon_path = config.get("icon_path", "")
            if icon_path and os.path.exists(icon_path):
                winreg.SetValueEx(key, "Icon", 0, winreg.REG_SZ, icon_path)
                print(f"Đã thêm icon: {icon_path}")
        
        command_key_path = pdf_key_path + r"\command"
        with winreg.CreateKey(winreg.HKEY_CLASSES_ROOT, command_key_path) as key:
            command = f'"{python_path}" "{script_path}" "%1"'
            winreg.SetValue(key, "", winreg.REG_SZ, command)
            print(f"Đã đăng ký command: {command}")
        
        print("✓ Context menu chính đã được đăng ký thành công!")
        print(f"Hãy thử nhấn chuột phải vào file PDF và chọn '{menu_text}'")
        return True
        
    except PermissionError:
        print("✗ Cần quyền Administrator để đăng ký context menu")
        return False
    except Exception as e:
        print(f"✗ Lỗi khi đăng ký context menu: {e}")
        return False

def unregister_main_context_menu():
    """Hủy đăng ký context menu chính"""
    try:
        winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF\command")
        winreg.DeleteKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF")
        print("✓ Context menu chính đã được hủy đăng ký")
        return True
    except FileNotFoundError:
        print("Context menu chính chưa được đăng ký")
        return False
    except Exception as e:
        print(f"✗ Lỗi khi hủy đăng ký: {e}")
        return False

def check_context_menu():
    """Kiểm tra context menu"""
    try:
        with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF") as key:
            menu_text = winreg.QueryValue(key, "")
            print(f"✓ Context menu đã được đăng ký: '{menu_text}'")
            
        with winreg.OpenKey(winreg.HKEY_CLASSES_ROOT, r"*\shell\RenamePDF\command") as key:
            command = winreg.QueryValue(key, "")
            print(f"Command: {command}")
            
        return True
    except FileNotFoundError:
        print("✗ Context menu chưa được đăng ký")
        return False
    except Exception as e:
        print(f"✗ Lỗi khi kiểm tra: {e}")
        return False

def main():
    print("=== QUẢN LÝ CONTEXT MENU CHÍNH ===")
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "unregister":
            unregister_main_context_menu()
        elif sys.argv[1] == "check":
            check_context_menu()
        else:
            print("Sử dụng: python register_main_context_menu.py [register|unregister|check]")
    else:
        print("Đang đăng ký context menu chính...")
        if register_main_context_menu():
            print("\nKiểm tra lại:")
            check_context_menu()

if __name__ == "__main__":
    main()
