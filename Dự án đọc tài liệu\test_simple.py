#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test đơn giản nhất
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
from datetime import datetime

def main():
    try:
        # Tạo log file
        script_dir = os.path.dirname(os.path.abspath(__file__))
        log_file = os.path.join(script_dir, 'test_simple.log')
        
        # Ghi log
        with open(log_file, 'a', encoding='utf-8') as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"\n{timestamp} - Script started\n")
            f.write(f"Arguments: {sys.argv}\n")
            f.write(f"Working dir: {os.getcwd()}\n")
        
        # Tạo GUI
        root = tk.Tk()
        root.withdraw()
        
        if len(sys.argv) > 1:
            pdf_path = sys.argv[1]
            filename = os.path.basename(pdf_path)
            
            message = f"CONTEXT MENU HOẠT ĐỘNG!\n\nFile: {filename}\n\nScript đã nhận được file PDF và có thể xử lý."
            messagebox.showinfo("Thành công!", message)
            
            # Ghi log thành công
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"SUCCESS: Processed {filename}\n")
        else:
            messagebox.showerror("Lỗi", "Không có file PDF được chọn!")
            
            # Ghi log lỗi
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write("ERROR: No PDF file provided\n")
        
        root.destroy()
        
    except Exception as e:
        # Ghi log lỗi
        try:
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"EXCEPTION: {str(e)}\n")
        except:
            pass
        
        # Hiển thị lỗi
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("Lỗi", f"Script bị lỗi: {str(e)}")
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    main()
