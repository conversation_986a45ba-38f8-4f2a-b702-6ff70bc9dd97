#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script cho Batch PDF Processor
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_pdfs():
    """Tạo một số file PDF test"""
    print("🧪 Creating test PDF files...")
    
    # Create temp directory
    test_dir = Path(tempfile.gettempdir()) / "batch_pdf_test"
    test_dir.mkdir(exist_ok=True)
    
    # Find existing PDF in project
    project_dir = Path(__file__).parent
    existing_pdfs = list(project_dir.glob("*.pdf"))
    
    if existing_pdfs:
        # Copy existing PDF to create test files
        source_pdf = existing_pdfs[0]
        print(f"📄 Using source PDF: {source_pdf.name}")
        
        test_files = []
        for i in range(3):
            test_file = test_dir / f"test_document_{i+1}.pdf"
            shutil.copy2(source_pdf, test_file)
            test_files.append(test_file)
            print(f"   ✅ Created: {test_file.name}")
        
        return test_dir, test_files
    else:
        print("❌ No existing PDF found in project directory")
        return None, []

def test_batch_processor_import():
    """Test import batch processor"""
    print("\n🧪 Testing Batch PDF Processor import...")
    
    try:
        from batch_pdf_processor import BatchPDFProcessor, PDFProcessorThread
        print("✅ BatchPDFProcessor imported successfully")
        
        # Test dependencies
        try:
            from PyQt5.QtWidgets import QApplication
            print("✅ PyQt5 available")
        except ImportError:
            print("❌ PyQt5 not available")
            return False
        
        try:
            from rename_pdf import process_pdf_with_progress
            print("✅ rename_pdf module available")
        except ImportError:
            print("❌ rename_pdf module not available")
        
        try:
            from config_manager import ConfigManager
            print("✅ config_manager module available")
        except ImportError:
            print("❌ config_manager module not available")
        
        try:
            from simple_progress import SimpleProgressManager
            print("✅ simple_progress module available")
        except ImportError:
            print("❌ simple_progress module not available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_batch_processor_gui():
    """Test GUI creation (without showing)"""
    print("\n🧪 Testing GUI creation...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from batch_pdf_processor import BatchPDFProcessor
        
        # Create application
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create window (don't show)
        window = BatchPDFProcessor()
        print("✅ BatchPDFProcessor window created successfully")
        
        # Test basic properties
        print(f"   📏 Window size: {window.size().width()}x{window.size().height()}")
        print(f"   📝 Window title: {window.windowTitle()}")
        print(f"   📄 Initial file count: {len(window.pdf_files)}")
        
        # Test UI components
        if hasattr(window, 'file_list'):
            print("   ✅ File list widget exists")
        if hasattr(window, 'progress_text'):
            print("   ✅ Progress text widget exists")
        if hasattr(window, 'results_text'):
            print("   ✅ Results text widget exists")
        if hasattr(window, 'start_btn'):
            print("   ✅ Start button exists")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_operations():
    """Test file operations"""
    print("\n🧪 Testing file operations...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from batch_pdf_processor import BatchPDFProcessor
        
        # Create application
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # Create window
        window = BatchPDFProcessor()
        
        # Create test files
        test_dir, test_files = create_test_pdfs()
        if not test_files:
            print("❌ No test files available")
            return False
        
        # Test adding files programmatically
        print("📁 Testing file operations...")
        
        initial_count = len(window.pdf_files)
        print(f"   Initial file count: {initial_count}")
        
        # Add files manually
        for test_file in test_files:
            if str(test_file) not in window.pdf_files:
                window.pdf_files.append(str(test_file))
                print(f"   ➕ Added: {test_file.name}")
        
        final_count = len(window.pdf_files)
        print(f"   Final file count: {final_count}")
        
        if final_count > initial_count:
            print("✅ File addition test passed")
        else:
            print("❌ File addition test failed")
        
        # Test UI state update
        window.update_ui_state()
        print("✅ UI state update test passed")
        
        # Cleanup
        if test_dir and test_dir.exists():
            shutil.rmtree(test_dir)
            print("🗑️ Cleaned up test files")
        
        return True
        
    except Exception as e:
        print(f"❌ File operations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_guide():
    """Hiển thị hướng dẫn sử dụng"""
    print("\n📖 Batch PDF Processor Usage Guide:")
    print("=" * 50)
    
    print("\n🚀 Cách chạy:")
    print("   python batch_pdf_processor.py")
    
    print("\n📋 Tính năng chính:")
    print("   ✅ Chọn nhiều file PDF cùng lúc")
    print("   ✅ Xử lý batch với progress tracking")
    print("   ✅ Hiển thị kết quả chi tiết")
    print("   ✅ Tiếp tục khi có lỗi")
    print("   ✅ Tự động cuộn log")
    print("   ✅ Xóa file được chọn")
    
    print("\n🎯 Cách sử dụng:")
    print("   1. Nhấn '➕ Thêm file' để chọn PDF")
    print("   2. Hoặc '📁 Thêm thư mục' để chọn tất cả PDF trong folder")
    print("   3. Cấu hình settings nếu cần")
    print("   4. Nhấn '🚀 Bắt đầu xử lý'")
    print("   5. Theo dõi tiến trình và kết quả")
    
    print("\n⚙️ Settings:")
    print("   📄 Tiếp tục khi lỗi: Không dừng khi 1 file bị lỗi")
    print("   📜 Tự động cuộn: Auto scroll log xuống")
    print("   🔍 Hiển thị chi tiết: Show detailed progress")
    
    print("\n🎨 UI Components:")
    print("   📁 File list: Danh sách PDF đã chọn")
    print("   📊 Progress: Tiến trình xử lý real-time")
    print("   📋 Results: Kết quả từng file")
    print("   🎛️ Controls: Start/Stop/Config buttons")

def main():
    print("🚀 Testing Batch PDF Processor\n")
    
    # Test 1: Import
    import_success = test_batch_processor_import()
    
    # Test 2: GUI creation
    if import_success:
        gui_success = test_batch_processor_gui()
        
        # Test 3: File operations
        if gui_success:
            file_ops_success = test_file_operations()
        else:
            file_ops_success = False
    else:
        gui_success = False
        file_ops_success = False
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"   Import test: {'✅' if import_success else '❌'}")
    print(f"   GUI test: {'✅' if gui_success else '❌'}")
    print(f"   File ops test: {'✅' if file_ops_success else '❌'}")
    
    if import_success and gui_success:
        print(f"\n🎉 Batch PDF Processor is ready to use!")
        show_usage_guide()
        
        print(f"\n💡 To run the application:")
        print(f"   python batch_pdf_processor.py")
    else:
        print(f"\n❌ Some tests failed. Check dependencies.")
        print(f"\n📋 Required dependencies:")
        print(f"   - PyQt5")
        print(f"   - rename_pdf.py")
        print(f"   - config_manager.py") 
        print(f"   - simple_progress.py")

if __name__ == "__main__":
    main()
