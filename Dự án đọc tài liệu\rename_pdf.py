import sys
import os
from datetime import datetime
import re
import google.generativeai as genai
import openai
from groq import Groq
from google.cloud import vision
from google.cloud import vision_v1
from pdf2image import convert_from_path
import json
import gspread
import win32gui
import win32con
from config_manager import ConfigManager
from google.oauth2 import service_account
import logging
import traceback
import time
import threading
try:
    from simple_progress import SimpleProgressManager as ProgressManager
except ImportError:
    # Fallback nếu không có simple_progress
    class ProgressManager:
        def __init__(self, title): pass
        def set_steps(self, steps): pass
        def start_step(self, i, msg): logger.info(f"Step {i}: {msg}")
        def update_step_progress(self, pct, msg): logger.info(f"{pct}%: {msg}")
        def complete_step(self, msg): logger.info(f"Complete: {msg}")
        def set_completed(self, success, msg): logger.info(f"Done: {success} - {msg}")
        def is_cancelled(self): return False
        def show_and_run(self, func): func(self)

# Thiết lập logging
def setup_logging():
    """Thiết lập logging cho debug"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    log_file = os.path.join(script_dir, 'rename_pdf_debug.log')

    # Tạo logger
    logger = logging.getLogger('rename_pdf')
    logger.setLevel(logging.DEBUG)

    # Xóa handlers cũ nếu có
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # File handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# Khởi tạo logger
logger = setup_logging()

def clean_filename_vn(s):
    """Làm sạch tên file tiếng Việt"""
    logger.debug(f"Cleaning filename: '{s}'")
    original = s
    s = s.strip()
    s = re.sub(r'[\\/:*?"<>|]', '.', s)
    s = re.sub(r'\.+', '.', s)
    s = re.sub(r'\s+', ' ', s)
    result = s.strip('. ')
    logger.debug(f"Cleaned filename: '{original}' -> '{result}'")
    return result

def show_notification(title, message):
    """Hiển thị thông báo Windows"""
    logger.info(f"Notification: {title} - {message}")
    try:
        win32gui.MessageBox(0, message, title, win32con.MB_OK | win32con.MB_ICONINFORMATION)
    except Exception as e:
        logger.error(f"Lỗi khi hiển thị notification: {e}")
        print(f"{title}: {message}")  # Fallback to console

def get_ai_client(config):
    """Khởi tạo client cho AI provider được chọn - Latest version compatible"""
    provider = config.get_ai_provider()
    model = config.get_selected_model()

    logger.info(f"Khởi tạo AI client - Provider: {provider}, Model: {model}")

    try:
        if provider == 'gemini':
            api_key = config.get_api_key('google')
            logger.debug(f"Gemini API key length: {len(api_key) if api_key else 0}")

            if not api_key:
                raise ValueError("Chưa cấu hình Google API Key")

            genai.configure(api_key=api_key)
            client = genai.GenerativeModel(model)
            logger.info("Khởi tạo Gemini client thành công")
            return client

        elif provider == 'openai':
            api_key = config.get_api_key('openai')
            logger.debug(f"OpenAI API key length: {len(api_key) if api_key else 0}")

            if not api_key:
                raise ValueError("Chưa cấu hình OpenAI API Key")

            # Khởi tạo OpenAI client với version detection
            try:
                # Check OpenAI version first
                version = getattr(openai, '__version__', '0.0.0')
                major_version = int(version.split('.')[0])
                logger.info(f"OpenAI version detected: {version}")

                if major_version >= 1:
                    # Try modern OpenAI client (v1.0+)
                    client = openai.OpenAI(api_key=api_key)
                    logger.info("Khởi tạo OpenAI client thành công (modern v1.0+)")
                    return client, model
                else:
                    # Use legacy style for v0.x
                    openai.api_key = api_key
                    logger.info("Khởi tạo OpenAI client thành công (legacy v0.x)")
                    return openai, model

            except Exception as e:
                logger.error(f"Lỗi khởi tạo OpenAI client: {e}")
                # Last resort fallback
                try:
                    openai.api_key = api_key
                    logger.info("Khởi tạo OpenAI client thành công (fallback)")
                    return openai, model
                except Exception as e2:
                    logger.error(f"Fallback cũng thất bại: {e2}")
                    raise ValueError(f"Không thể khởi tạo OpenAI client: {e2}")

        elif provider == 'grok':
            api_key = config.get_api_key('grok')
            logger.debug(f"Grok API key length: {len(api_key) if api_key else 0}")

            if not api_key:
                raise ValueError("Chưa cấu hình Grok API Key")

            try:
                from groq import Groq

                # Try different initialization methods for Groq compatibility
                try:
                    client = Groq(api_key=api_key)
                    logger.info("Khởi tạo Grok client thành công (modern)")
                except TypeError as type_error:
                    if "proxies" in str(type_error):
                        logger.warning(f"Groq proxies parameter issue: {type_error}")
                        try:
                            # Try alternative initialization
                            import groq
                            client = groq.Groq(api_key=api_key)
                            logger.info("Khởi tạo Grok client thành công (alternative)")
                        except Exception:
                            # Last resort
                            client = Groq()
                            client.api_key = api_key
                            logger.info("Khởi tạo Grok client thành công (fallback)")
                    else:
                        raise type_error

                return client, model

            except ImportError:
                raise ValueError("Chưa cài đặt thư viện groq. Chạy: pip install groq")

        else:
            error_msg = f"Không hỗ trợ AI provider: {provider}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    except Exception as e:
        logger.error(f"Lỗi khi khởi tạo AI client: {e}")
        logger.error(traceback.format_exc())

        # Provide specific error messages
        error_msg = str(e)
        if "api key" in error_msg.lower():
            raise ValueError(f"Lỗi API Key cho {provider}: {error_msg}")
        elif "import" in error_msg.lower():
            raise ValueError(f"Thiếu thư viện cho {provider}: {error_msg}")
        else:
            raise ValueError(f"Lỗi khởi tạo {provider} client: {error_msg}")

def generate_with_ai(client, prompt, provider, image_data=None):
    """Tạo nội dung với AI provider được chọn - Latest version compatible"""
    logger.debug(f"Generating content with {provider}")
    if image_data:
        logger.debug(f"Using image data, size: {len(image_data)} bytes")
    else:
        logger.debug(f"Prompt length: {len(prompt)}")

    start_time = time.time()

    try:
        if provider == 'gemini':
            logger.debug("Calling Gemini API...")
            if image_data:
                # Sử dụng Gemini Vision với ảnh
                import PIL.Image
                from io import BytesIO

                # Chuyển đổi image data thành PIL Image
                pil_image = PIL.Image.open(BytesIO(image_data))

                # Gọi Gemini với ảnh
                response = client.generate_content([prompt, pil_image])
            else:
                # Gọi Gemini với text thông thường
                response = client.generate_content(prompt)

            result = response.text.strip() if hasattr(response, 'text') else ''
            logger.debug(f"Gemini response length: {len(result)}")

        elif provider == 'openai':
            logger.debug("Calling OpenAI API...")

            # Determine if client is tuple (legacy) or direct client (modern)
            if isinstance(client, tuple):
                # Legacy format: (client, model)
                openai_client, model = client
            else:
                # Modern format: direct client, get model from config
                openai_client = client
                from config_manager import ConfigManager
                config = ConfigManager()
                model = config.get_selected_model()

            # Detect client type safely
            try:
                is_modern_client = (hasattr(openai_client, 'chat') and
                                  hasattr(openai_client.chat, 'completions') and
                                  hasattr(openai_client.chat.completions, 'create'))
            except AttributeError:
                is_modern_client = False

            logger.debug(f"OpenAI client type: {'modern' if is_modern_client else 'legacy'}")

            if image_data:
                # Sử dụng OpenAI Vision với ảnh
                import base64

                # Encode ảnh thành base64
                image_base64 = base64.b64encode(image_data).decode('utf-8')

                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ]

                try:
                    if is_modern_client:
                        response = openai_client.chat.completions.create(
                            model=model,
                            messages=messages,
                            timeout=30
                        )
                    else:
                        # Legacy client - vision might not be supported
                        logger.warning("Legacy OpenAI client - vision may not be supported, using text fallback")
                        response = openai_client.ChatCompletion.create(
                            model=model,
                            messages=[{"role": "user", "content": prompt}],
                            timeout=30
                        )
                except AttributeError as attr_error:
                    logger.error(f"OpenAI client attribute error: {attr_error}")
                    raise ValueError(f"OpenAI client không tương thích. Chạy: python fix_openai_chat_error.py")
            else:
                # Gọi OpenAI với text thông thường
                try:
                    if is_modern_client:
                        response = openai_client.chat.completions.create(
                            model=model,
                            messages=[{"role": "user", "content": prompt}],
                            timeout=30
                        )
                    else:
                        # Legacy client
                        response = openai_client.ChatCompletion.create(
                            model=model,
                            messages=[{"role": "user", "content": prompt}],
                            timeout=30
                        )
                except AttributeError as attr_error:
                    logger.error(f"OpenAI client attribute error: {attr_error}")
                    raise ValueError(f"OpenAI client không tương thích. Chạy: python fix_openai_chat_error.py")

            result = response.choices[0].message.content.strip()
            logger.debug(f"OpenAI response length: {len(result)}")

        elif provider == 'grok':
            logger.debug("Calling Grok API...")

            # Determine if client is tuple or direct client
            if isinstance(client, tuple):
                grok_client, model = client
            else:
                grok_client = client
                from config_manager import ConfigManager
                config = ConfigManager()
                model = config.get_selected_model()

            if image_data:
                logger.warning("Grok không hỗ trợ vision, sẽ sử dụng text fallback")
                # Grok chưa hỗ trợ vision, fallback về text
                response = grok_client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    timeout=30
                )
            else:
                response = grok_client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    timeout=30
                )

            result = response.choices[0].message.content.strip()
            logger.debug(f"Grok response length: {len(result)}")

        else:
            error_msg = f"Không hỗ trợ AI provider: {provider}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        elapsed_time = time.time() - start_time
        logger.info(f"AI generation completed in {elapsed_time:.2f} seconds")
        logger.debug(f"AI response preview: {result[:200]}...")

        return result

    except Exception as e:
        elapsed_time = time.time() - start_time
        error_msg = str(e)

        # Provide specific error handling
        if "timeout" in error_msg.lower():
            logger.error(f"Timeout khi gọi {provider} API sau {elapsed_time:.2f}s")
        elif "rate limit" in error_msg.lower():
            logger.error(f"Rate limit {provider} API")
        elif "authentication" in error_msg.lower():
            logger.error(f"Lỗi authentication {provider} API")
        else:
            logger.error(f"Lỗi khi generate với AI ({provider}) sau {elapsed_time:.2f}s: {e}")

        logger.error(traceback.format_exc())
        raise

def process_pdf_with_progress(pdf_path, progress_manager):
    """Xử lý file PDF với progress dialog"""
    logger.info("="*60)
    logger.info(f"BẮT ĐẦU XỬ LÝ PDF: {pdf_path}")
    logger.info("="*60)

    start_time = time.time()

    try:
        # Bước 1: Kiểm tra file PDF
        progress_manager.start_step(0, f"Kiểm tra file: {os.path.basename(pdf_path)}")

        if not os.path.exists(pdf_path):
            error_msg = f"File PDF không tồn tại: {pdf_path}"
            logger.error(error_msg)
            progress_manager.set_completed(False, error_msg)
            return

        if not pdf_path.lower().endswith('.pdf'):
            error_msg = f"File không phải là PDF: {pdf_path}"
            logger.error(error_msg)
            progress_manager.set_completed(False, error_msg)
            return

        file_size = os.path.getsize(pdf_path)
        logger.info(f"File PDF: {os.path.basename(pdf_path)}")
        logger.info(f"Kích thước: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")

        progress_manager.update_step_progress(30, f"File hợp lệ: {file_size/1024/1024:.2f} MB")

        # Khởi tạo ConfigManager với base_dir là thư mục của script rename_pdf.py
        script_dir = os.path.dirname(os.path.abspath(__file__))
        logger.info(f"Script directory: {script_dir}")

        config = ConfigManager(base_dir=script_dir)
        logger.info(f"Config file: {config.config_file}")

        progress_manager.update_step_progress(60, "Đang kiểm tra cấu hình...")

        # Kiểm tra sự tồn tại của config.json
        if not os.path.exists(config.config_file):
            error_msg = (f"Không tìm thấy file cấu hình: {config.config_file}\n"
                        "Vui lòng đảm bảo file config.json nằm cùng thư mục với rename_pdf.py "
                        "hoặc chạy config_manager.py để tạo file cấu hình.")
            logger.error(error_msg)
            progress_manager.set_completed(False, error_msg)
            return

        progress_manager.update_step_progress(100, "Cấu hình đã sẵn sàng")
        logger.info("Cấu hình đã được tải thành công")
        progress_manager.complete_step("Kiểm tra hoàn tất")

        # Bước 2: Chuyển tất cả các trang PDF thành ảnh
        progress_manager.start_step(1, "Đang chuyển đổi PDF thành ảnh...")
        logger.info("Bắt đầu chuyển đổi PDF thành ảnh...")
        pdf_start_time = time.time()

        try:
            images = convert_from_path(pdf_path)
            pdf_time = time.time() - pdf_start_time
            logger.info(f"Chuyển đổi PDF hoàn tất trong {pdf_time:.2f}s - Số trang: {len(images)}")
            progress_manager.update_step_progress(100, f"Chuyển đổi hoàn tất: {len(images)} trang trong {pdf_time:.2f}s")
        except Exception as e:
            logger.error(f"Lỗi khi chuyển đổi PDF: {e}")
            logger.error(traceback.format_exc())
            progress_manager.set_completed(False, f"Không thể chuyển đổi PDF: {e}")
            return

        if not images:
            error_msg = "Không thể chuyển đổi PDF thành ảnh - không có trang nào"
            logger.error(error_msg)
            progress_manager.set_completed(False, error_msg)
            return

        progress_manager.complete_step(f"Đã chuyển đổi {len(images)} trang")

        # Bước 3: OCR với Google Vision
        progress_manager.start_step(2, "Đang OCR văn bản...")
        logger.info("Bắt đầu OCR với Google Vision...")
        ocr_start_time = time.time()

        try:
            # Vision client cần được khởi tạo với credentials từ ConfigManager
            credentials_file = config.get_google_credentials_file()
            logger.debug(f"Credentials file: {credentials_file}")

            creds = service_account.Credentials.from_service_account_file(credentials_file)
            vision_client = vision.ImageAnnotatorClient(credentials=creds)
            logger.debug("Vision client khởi tạo thành công")

            progress_manager.update_step_progress(10, "Đã khởi tạo Vision client")

            full_text = []

            # OCR tất cả các trang
            for i, image in enumerate(images, 1):
                if progress_manager.is_cancelled():
                    logger.info("OCR bị hủy bởi người dùng")
                    return

                logger.debug(f"OCR trang {i}/{len(images)}")
                page_start_time = time.time()

                try:
                    from io import BytesIO
                    buf = BytesIO()
                    image.save(buf, format='PNG')
                    img_bytes = buf.getvalue()

                    vision_image = vision_v1.types.Image(content=img_bytes)
                    response = vision_client.text_detection(
                        image=vision_image,
                        image_context={"language_hints": ["vi"]}
                    )

                    texts = response.text_annotations
                    page_text = texts[0].description if texts else ''
                    full_text.append(page_text)

                    page_time = time.time() - page_start_time
                    text_length = len(page_text)
                    logger.debug(f"Trang {i} OCR hoàn tất trong {page_time:.2f}s - {text_length} ký tự")

                    # Update progress
                    page_progress = 10 + (i / len(images)) * 80  # 10-90%
                    progress_manager.update_step_progress(
                        page_progress,
                        f"OCR trang {i}/{len(images)} - {text_length} ký tự"
                    )

                except Exception as e:
                    logger.error(f"Lỗi OCR trang {i}: {e}")
                    full_text.append('')  # Thêm trang trống nếu lỗi

            ocr_time = time.time() - ocr_start_time
            combined_text = "\n".join(full_text)
            total_chars = len(combined_text)
            logger.info(f"OCR hoàn tất trong {ocr_time:.2f}s - Tổng {total_chars:,} ký tự")
            logger.debug(f"Text preview: {combined_text[:500]}...")

            progress_manager.update_step_progress(100, f"OCR hoàn tất: {total_chars:,} ký tự trong {ocr_time:.2f}s")
            progress_manager.complete_step(f"OCR {len(images)} trang thành công")

        except Exception as e:
            logger.error(f"Lỗi trong quá trình OCR: {e}")
            logger.error(traceback.format_exc())
            progress_manager.set_completed(False, f"Lỗi OCR: {e}")
            return

        # Bước 4: Trích xuất thông tin với AI
        progress_manager.start_step(3, "Đang khởi tạo AI client...")

        # Khởi tạo AI client
        logger.info("Khởi tạo AI client...")
        try:
            ai_client = get_ai_client(config)
            provider = config.get_ai_provider()
            progress_manager.update_step_progress(20, f"Đã khởi tạo {provider} client")
        except Exception as e:
            logger.error(f"Lỗi khởi tạo AI client: {e}")
            progress_manager.set_completed(False, f"Lỗi khởi tạo AI: {e}")
            return

        # Trích xuất thông tin từ ảnh trang đầu
        logger.info("Bắt đầu trích xuất thông tin từ ảnh trang đầu...")
        progress_manager.update_step_progress(30, "Đang chuẩn bị ảnh trang đầu...")

        # Lấy ảnh trang đầu tiên
        first_page_image = images[0] if images else None
        if not first_page_image:
            logger.error("Không có ảnh trang đầu để trích xuất thông tin")
            progress_manager.set_completed(False, "Không có ảnh trang đầu để trích xuất thông tin")
            return

        # Chuyển ảnh thành bytes
        try:
            from io import BytesIO
            buf = BytesIO()
            first_page_image.save(buf, format='PNG')
            first_page_image_data = buf.getvalue()
            logger.debug(f"First page image size: {len(first_page_image_data)} bytes")
            progress_manager.update_step_progress(40, f"Đã chuẩn bị ảnh: {len(first_page_image_data):,} bytes")
        except Exception as e:
            logger.error(f"Lỗi khi chuyển đổi ảnh: {e}")
            progress_manager.set_completed(False, f"Lỗi khi chuyển đổi ảnh: {e}")
            return

        # Tạo prompt chi tiết cho việc trích xuất thông tin
        extraction_prompt = """Hãy phân tích ảnh này và trích xuất thông tin theo đúng định dạng sau (nếu không tìm thấy thì để trống sau dấu hai chấm):

1. Ngày ký: <ngày ký, định dạng dd/mm/yyyy>
2. Số công văn: <số công văn>
3. Tiêu đề công văn: <tiêu đề công văn>

HƯỚNG DẪN CHI TIẾT:

**NGÀY KÝ:**
- Tìm thông tin ngày có định dạng: "Địa danh, ngày ... tháng ... năm..."
- Ví dụ: "Hà Nội, ngày 20 tháng 11 năm 2025" → Ngày ký: 20/11/2025
- Ví dụ: "TP.HCM, ngày 15 tháng 3 năm 2024" → Ngày ký: 15/03/2024
- Chỉ lấy số ngày, tháng, năm và chuyển về định dạng dd/mm/yyyy

**SỐ CÔNG VĂN:**
- Tìm dòng bắt đầu bằng "Số:" hoặc "SỐ:"
- Ví dụ: "Số: 20/TB-TTĐT" → Số công văn: 20/TB-TTĐT
- Ví dụ: "SỐ: 123/QĐ-UBND" → Số công văn: 123/QĐ-UBND
- Chỉ lấy phần sau dấu hai chấm, bỏ khoảng trắng thừa

**TIÊU ĐỀ CÔNG VĂN:**
- Tìm tiêu đề chính của văn bản, thường là dòng text lớn nhất hoặc in đậm
- Thường nằm ở giữa trang, sau phần header
- Bỏ qua các từ như "THÔNG BÁO", "QUYẾT ĐỊNH", "CÔNG VĂN" nếu chúng chỉ là loại văn bản
- Lấy nội dung chính của tiêu đề

Hãy trả về đúng 3 dòng theo định dạng trên."""

        try:
            progress_manager.update_step_progress(50, f"Đang gọi {provider} API...")

            # Gọi AI với ảnh
            file_info = generate_with_ai(ai_client, extraction_prompt, provider, first_page_image_data)
            logger.info("Trích xuất thông tin từ ảnh hoàn tất")
            logger.debug(f"Extracted info: {file_info}")
            progress_manager.update_step_progress(80, "Đã trích xuất thông tin từ ảnh")

        except Exception as e:
            logger.error(f"Lỗi khi trích xuất thông tin từ ảnh: {e}")
            logger.error(traceback.format_exc())

            # Fallback: sử dụng text nếu vision API lỗi
            logger.info("Fallback: Sử dụng text từ OCR...")
            progress_manager.update_step_progress(60, "Vision API lỗi, đang fallback sang text...")

            try:
                first_page_text = full_text[0] if full_text else ''
                logger.debug(f"First page text length: {len(first_page_text)}")

                text_extraction_prompt = f"""Từ văn bản OCR sau, hãy trích xuất thông tin theo đúng định dạng:

1. Ngày ký: <ngày ký, định dạng dd/mm/yyyy>
2. Số công văn: <số công văn>
3. Tiêu đề công văn: <tiêu đề công văn>

HƯỚNG DẪN:
- Ngày ký: Tìm "Địa danh, ngày ... tháng ... năm..." → chuyển về dd/mm/yyyy
- Số công văn: Tìm "Số:" hoặc "SỐ:" → lấy phần sau dấu hai chấm
- Tiêu đề: Tìm tiêu đề chính của văn bản

Văn bản OCR:
{first_page_text}"""

                file_info = generate_with_ai(ai_client, text_extraction_prompt, provider)
                logger.info("Trích xuất thông tin từ text fallback hoàn tất")
                logger.debug(f"Extracted info (fallback): {file_info}")
                progress_manager.update_step_progress(80, "Đã trích xuất thông tin từ text")

            except Exception as fallback_error:
                logger.error(f"Lỗi cả vision và text fallback: {fallback_error}")
                progress_manager.set_completed(False, f"Lỗi khi trích xuất thông tin: {e}")
                return

        # Parse thông tin
        logger.info("Phân tích thông tin đã trích xuất...")
        info_lines = [line for line in file_info.split('\n') if line.strip()]
        while len(info_lines) < 3:
            info_lines.append('')

        logger.debug(f"Info lines: {info_lines}")

        date = info_lines[0].split(': ', 1)[1] if len(info_lines) > 0 and ': ' in info_lines[0] else ''
        number = info_lines[1].split(': ', 1)[1] if len(info_lines) > 1 and ': ' in info_lines[1] else ''
        title = info_lines[2].split(': ', 1)[1] if len(info_lines) > 2 and ': ' in info_lines[2] else ''

        logger.info(f"Thông tin trích xuất:")
        logger.info(f"  Ngày ký: '{date}'")
        logger.info(f"  Số công văn: '{number}'")
        logger.info(f"  Tiêu đề: '{title}'")

        # Nếu không có tiêu đề, yêu cầu AI đặt tiêu đề dựa trên ảnh trang đầu
        if not title.strip():
            logger.info("Không có tiêu đề, yêu cầu AI tạo tiêu đề từ ảnh...")
            try:
                title_prompt = """Hãy phân tích ảnh này và đặt một tiêu đề ngắn gọn (không quá 15 từ) cho công văn này.

Hướng dẫn:
- Tìm nội dung chính của văn bản
- Bỏ qua các từ như "THÔNG BÁO", "QUYẾT ĐỊNH", "CÔNG VĂN"
- Tạo tiêu đề súc tích, dễ hiểu
- Không vượt quá 15 từ

Chỉ trả về tiêu đề, không giải thích thêm."""

                title = generate_with_ai(ai_client, title_prompt, provider, first_page_image_data)
                logger.info(f"AI tạo tiêu đề từ ảnh: '{title}'")
            except Exception as e:
                logger.error(f"Lỗi khi tạo tiêu đề từ ảnh: {e}")
                # Fallback: sử dụng text
                try:
                    title_prompt = f"""Dựa vào nội dung OCR sau, hãy đặt một tiêu đề ngắn gọn (không quá 15 từ) cho công văn này:
{combined_text[:2000]}"""  # Giới hạn text để tránh quá dài

                    title = generate_with_ai(ai_client, title_prompt, provider)
                    logger.info(f"AI tạo tiêu đề từ text fallback: '{title}'")
                except Exception as fallback_error:
                    logger.error(f"Lỗi cả ảnh và text fallback cho tiêu đề: {fallback_error}")
                    title = "Công văn"  # Fallback title

        progress_manager.complete_step("Trích xuất thông tin hoàn tất")

        # Bước 5: Tóm tắt và lưu kết quả
        progress_manager.start_step(4, "Đang tóm tắt nội dung...")

        # Tóm tắt nội dung
        logger.info("Bắt đầu tóm tắt nội dung...")
        try:
            summary_prompt = f"Tóm tắt văn bản sau bằng tiếng Việt:\n{combined_text}"
            summary = generate_with_ai(ai_client, summary_prompt, provider)
            logger.info(f"Tóm tắt hoàn tất - Độ dài: {len(summary)} ký tự")
            logger.debug(f"Summary preview: {summary[:200]}...")
            progress_manager.update_step_progress(30, f"Tóm tắt hoàn tất: {len(summary)} ký tự")
        except Exception as e:
            logger.error(f"Lỗi khi tóm tắt: {e}")
            summary = "Không thể tóm tắt nội dung"
            progress_manager.update_step_progress(30, "Lỗi tóm tắt, sử dụng fallback")

        # Làm sạch thông tin
        logger.info("Làm sạch và xử lý thông tin...")
        date_clean = re.sub(r'[^0-9/]', '', date)
        logger.debug(f"Date cleaned: '{date}' -> '{date_clean}'")

        try:
            date_obj = datetime.strptime(date_clean, '%d/%m/%Y') if date_clean else datetime.now()
            logger.debug(f"Date parsed: {date_obj}")
        except Exception as e:
            logger.warning(f"Không thể parse ngày '{date_clean}': {e}")
            date_obj = datetime.now()

        number_clean = clean_filename_vn(number)
        title_clean = clean_filename_vn(title)

        # Tạo tên file mới
        filename = f"{date_obj.strftime('%Y%m%d')}.{number_clean} {title_clean}".strip()
        logger.debug(f"Generated filename: '{filename}'")

        if len(filename) > 200:
            filename = filename[:200]
            logger.warning(f"Filename truncated to 200 chars: '{filename}'")

        # Đổi tên file
        progress_manager.update_step_progress(50, "Đang đổi tên file...")
        new_path = os.path.join(os.path.dirname(pdf_path), f"{filename}.pdf")
        logger.info(f"Đổi tên file: {os.path.basename(pdf_path)} -> {os.path.basename(new_path)}")

        try:
            os.rename(pdf_path, new_path)
            logger.info("Đổi tên file thành công")
            progress_manager.update_step_progress(70, f"Đã đổi tên: {os.path.basename(new_path)}")
        except Exception as e:
            logger.error(f"Lỗi khi đổi tên file: {e}")
            logger.error(traceback.format_exc())
            progress_manager.set_completed(False, f"Không thể đổi tên file: {e}")
            return

        # Ghi Google Sheet
        progress_manager.update_step_progress(80, "Đang ghi Google Sheet...")
        logger.info("Bắt đầu ghi dữ liệu vào Google Sheet...")
        sheet_start_time = time.time()

        try:
            credentials_file = config.get_google_credentials_file()
            sheet_url = config.get_google_sheet_url()

            logger.debug(f"Sheet URL: {sheet_url}")
            logger.debug(f"Credentials file: {credentials_file}")

            gc = gspread.service_account(filename=credentials_file)

            if sheet_url.startswith('http'):
                sh = gc.open_by_url(sheet_url)
            else:
                sh = gc.open_by_key(sheet_url)

            worksheet = sh.sheet1
            pdf_link = f"file:///{os.path.abspath(new_path).replace('\\','/')}"

            row_data = [
                date_obj.strftime('%d/%m/%Y'),
                number_clean,
                title_clean,
                summary,
                pdf_link
            ]

            logger.debug(f"Row data: {row_data}")
            worksheet.append_row(row_data)

            sheet_time = time.time() - sheet_start_time
            logger.info(f"Ghi Google Sheet hoàn tất trong {sheet_time:.2f}s")

            # Hiển thị thông báo thành công
            sheet_name = config.get_google_sheet_name() or sh.title
            total_time = time.time() - start_time

            success_msg = (f"Đã đổi tên file thành: {filename}.pdf\n"
                          f"Đã lưu dữ liệu vào Google Sheet: {sheet_name}\n"
                          f"Tổng thời gian xử lý: {total_time:.2f}s")

            logger.info("="*60)
            logger.info("XỬ LÝ HOÀN TẤT THÀNH CÔNG!")
            logger.info(f"File mới: {new_path}")
            logger.info(f"Google Sheet: {sheet_name}")
            logger.info(f"Tổng thời gian: {total_time:.2f}s")
            logger.info("="*60)

            progress_manager.update_step_progress(100, f"Đã lưu vào {sheet_name}")
            progress_manager.complete_step("Lưu kết quả hoàn tất")
            progress_manager.set_completed(True, success_msg)

        except Exception as e:
            sheet_time = time.time() - sheet_start_time
            logger.error(f"Lỗi khi ghi Google Sheet sau {sheet_time:.2f}s: {e}")
            logger.error(traceback.format_exc())
            progress_manager.set_completed(False, f"Lỗi khi ghi Google Sheet: {str(e)}")

    except Exception as e:
        total_time = time.time() - start_time
        logger.error("="*60)
        logger.error(f"LỖI TỔNG QUÁT sau {total_time:.2f}s: {e}")
        logger.error(traceback.format_exc())
        logger.error("="*60)
        progress_manager.set_completed(False, f"Lỗi: {str(e)}")

def process_pdf(pdf_path):
    """Wrapper function để chạy với progress dialog"""
    logger.info(f"Starting process_pdf wrapper for: {pdf_path}")

    def work_function(progress_dialog):
        """Function chạy xử lý PDF với progress dialog"""
        try:
            # Tạo progress manager từ dialog
            progress_manager = ProgressManager("Xử lý PDF - " + os.path.basename(pdf_path))
            progress_manager.dialog = progress_dialog  # Sử dụng dialog đã có

            # Định nghĩa các bước
            steps = [
                ("Kiểm tra file và cấu hình", 10),
                ("Chuyển đổi PDF thành ảnh", 20),
                ("OCR văn bản", 30),
                ("Trích xuất thông tin với AI", 25),
                ("Lưu kết quả", 15)
            ]

            progress_manager.set_steps(steps)
            logger.info("Progress manager created successfully")

            # Chạy xử lý chính
            logger.info("Starting PDF processing...")
            process_pdf_with_progress(pdf_path, progress_manager)
            logger.info("PDF processing completed")

        except Exception as e:
            logger.error(f"Error in processing: {e}")
            logger.error(traceback.format_exc())
            progress_dialog.set_completed(False, f"Lỗi: {str(e)}")

    # Sử dụng simple progress
    try:
        from simple_progress import SimpleProgressDialog
        dialog = SimpleProgressDialog("Xử lý PDF - " + os.path.basename(pdf_path))
        dialog.show_and_run(work_function)
        logger.info("Progress dialog completed")

    except Exception as e:
        logger.error(f"Error with progress dialog: {e}")
        logger.error(traceback.format_exc())

        # Fallback: chạy trực tiếp mà không có progress dialog
        logger.info("Fallback: running without progress dialog")
        try:
            # Tạo dummy progress manager
            class DummyProgress:
                def start_step(self, step, msg): logger.info(f"Step {step}: {msg}")
                def update_step_progress(self, pct, msg): logger.info(f"{pct}%: {msg}")
                def complete_step(self, msg): logger.info(f"Complete: {msg}")
                def set_completed(self, success, msg):
                    logger.info(f"Finished: {success} - {msg}")
                    if success:
                        show_notification("Thành công", msg)
                    else:
                        show_notification("Lỗi", msg)
                def is_cancelled(self): return False

            dummy_progress = DummyProgress()
            process_pdf_with_progress(pdf_path, dummy_progress)
        except Exception as fallback_error:
            logger.error(f"Fallback also failed: {fallback_error}")
            show_notification("Lỗi", f"Lỗi nghiêm trọng: {fallback_error}")

if __name__ == "__main__":
    # Thêm import service_account ở đây để sử dụng trong rename_pdf.py
    from google.oauth2 import service_account

    # Kiểm tra nếu script được chạy như .pyw (ẩn console)
    script_name = os.path.basename(__file__)
    is_pyw = script_name.endswith('.pyw')

    logger.info("="*60)
    if is_pyw:
        logger.info("KHỞI ĐỘNG RENAME_PDF.PYW (NO CONSOLE)")
    else:
        logger.info("KHỞI ĐỘNG RENAME_PDF.PY")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Script path: {__file__}")
    logger.info(f"Working directory: {os.getcwd()}")
    logger.info(f"Arguments: {sys.argv}")
    logger.info("="*60)

    try:
        if len(sys.argv) > 1:
            pdf_path = sys.argv[1]
            logger.info(f"PDF path từ argument: {pdf_path}")

            if os.path.exists(pdf_path) and pdf_path.lower().endswith('.pdf'):
                logger.info("File PDF hợp lệ, bắt đầu xử lý...")
                process_pdf(pdf_path)
            else:
                error_msg = "File không tồn tại hoặc không phải file PDF"
                logger.error(f"{error_msg}: {pdf_path}")
                show_notification("Lỗi", error_msg)
        else:
            error_msg = "Vui lòng cung cấp đường dẫn file PDF"
            logger.error(error_msg)
            logger.info("Cách sử dụng: python rename_pdf.py <path_to_pdf_file>")
            show_notification("Lỗi", error_msg)

    except Exception as e:
        logger.error(f"Lỗi trong main: {e}")
        logger.error(traceback.format_exc())
        show_notification("Lỗi", f"Lỗi không mong muốn: {e}")

    if is_pyw:
        logger.info("KẾT THÚC RENAME_PDF.PYW")
    else:
        logger.info("KẾT THÚC RENAME_PDF.PY")
    logger.info("="*60)